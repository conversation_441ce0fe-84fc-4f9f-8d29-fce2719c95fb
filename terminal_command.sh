#!/bin/bash

# FlowIQ GitHub Setup Script
echo "🚀 Setting up FlowIQ project on GitHub..."

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: Not in the FlowIQ project directory"
    exit 1
fi

echo "📁 Current directory: $(pwd)"
echo "📊 Git status:"
git status

echo ""
echo "🔗 Current remote configuration:"
git remote -v

echo ""
echo "📝 Recent commits:"
git log --oneline -3

echo ""
echo "🎯 To push your changes to GitHub, you have two options:"
echo ""
echo "Option 1: Use Personal Access Token (Recommended)"
echo "1. Go to: https://github.com/settings/tokens"
echo "2. Click 'Generate new token (classic)'"
echo "3. Give it a name like 'FIQ-Project'"
echo "4. Select scopes: repo, workflow"
echo "5. Copy the token"
echo "6. Run: git push origin main"
echo "7. When prompted for password, paste the token"
echo ""
echo "Option 2: Set up SSH Keys"
echo "1. Run: ssh-keygen -t ed25519 -C '<EMAIL>'"
echo "2. Run: cat ~/.ssh/id_ed25519.pub"
echo "3. Copy the output and add it to GitHub SSH keys"
echo "4. Run: git remote set-<NAME_EMAIL>:BandsCo/FIQ.git"
echo "5. Run: git push origin main"
echo ""
echo "🌟 Your project will then be live at: https://github.com/BandsCo/FIQ"