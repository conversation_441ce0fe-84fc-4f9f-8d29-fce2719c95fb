# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.2.0](https://github.com/inspect-js/is-arguments/compare/v1.1.1...v1.2.0) - 2024-12-12

### Commits

- [actions] reuse common workflows [`ff7c948`](https://github.com/inspect-js/is-arguments/commit/ff7c94886dbfb6ea5d2175d5e49c1d0b4f4ac922)
- [New] add types [`5fe6c6e`](https://github.com/inspect-js/is-arguments/commit/5fe6c6e0d0826d86f355c7e95adb4838dbe6df36)
- [meta] use `npmignore` to autogenerate an npmignore file [`9a9b0cf`](https://github.com/inspect-js/is-arguments/commit/9a9b0cf7dcf00a8dad6dd38f983f7611b33a5c9d)
- [actions] split out node 10-20, and 20+ [`74c5c49`](https://github.com/inspect-js/is-arguments/commit/74c5c490763649e03d54f97f8367918ee710d6a5)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `safe-publish-latest`, `tape` [`d9a932d`](https://github.com/inspect-js/is-arguments/commit/d9a932db8a0ad7f95ebd3ccd5322a5d5ea3d422f)
- [readme] update URLs [`0f726b6`](https://github.com/inspect-js/is-arguments/commit/0f726b696ff562d5beecdd5a407579c9d18ec226)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `tape` [`ea21450`](https://github.com/inspect-js/is-arguments/commit/ea21450ecbfff962f273b1e741af2bf20241563d)
- [actions] update rebase action to use reusable workflow [`eaad2ab`](https://github.com/inspect-js/is-arguments/commit/eaad2ab2e25018037a3bcd149d65206bf723cb88)
- [actions] cleanup [`2be15d3`](https://github.com/inspect-js/is-arguments/commit/2be15d3ee78dd214d5b961d8c890aae94f0faa58)
- [actions] update codecov uploader [`29b929c`](https://github.com/inspect-js/is-arguments/commit/29b929cf44fa1014f431d89c44518c34bbcd774b)
- [Dev Deps] update `@ljharb/eslint-config`, `auto-changelog`, `npmignore`, `tape` [`8a38bf5`](https://github.com/inspect-js/is-arguments/commit/8a38bf572c59fdfdf6db9167185f78c59410048b)
- [Dev Deps] update `@ljharb/eslint-config`, `safe-publish-latest`, `tape` [`841627d`](https://github.com/inspect-js/is-arguments/commit/841627ddcc32f578b62b659dede177697737d027)
- [Refactor] use `call-bound` directly [`e429257`](https://github.com/inspect-js/is-arguments/commit/e429257b9e3d28b60cbc86c6f1c877e23af28429)
- [Deps] update `call-bind`, `has-tostringtag` [`c0db6a8`](https://github.com/inspect-js/is-arguments/commit/c0db6a800366e9744c375560813cbd70bf8e0d2d)
- [Tests] replace `aud` with `npm audit` [`f589983`](https://github.com/inspect-js/is-arguments/commit/f589983d330f205f89af679af6d4b806a3cbceb5)
- [meta] add `sideEffects` flag [`2e15793`](https://github.com/inspect-js/is-arguments/commit/2e15793dfd3ef97b0e4a311be056bdfbaa9f5beb)
- [Dev Deps] add missing peer dep [`0ffe01b`](https://github.com/inspect-js/is-arguments/commit/0ffe01b8ecce5a30b93bf6078e62a3c6268254b8)

## [v1.1.1](https://github.com/inspect-js/is-arguments/compare/v1.1.0...v1.1.1) - 2021-08-05

### Commits

- [actions] use `node/install` instead of `node/run`; use `codecov` action [`dd28b30`](https://github.com/inspect-js/is-arguments/commit/dd28b30f4237fac722f2ce05b0c1d7e63c4a81e4)
- [meta] do not publish github action workflow files [`87e489c`](https://github.com/inspect-js/is-arguments/commit/87e489cc77b709b96e73aaf9f9b2cd6da48f4960)
- [readme] fix repo URLs [`e2c2c6e`](https://github.com/inspect-js/is-arguments/commit/e2c2c6ee34ca21be4b19d282d96dd7ab75b63ae3)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `tape` [`b9ae62b`](https://github.com/inspect-js/is-arguments/commit/b9ae62b3a08a5fe84519865192e6287d5b6966f7)
- [readme] add github actions/codecov badges [`504c0a5`](https://github.com/inspect-js/is-arguments/commit/504c0a508dc313eae5942b1e35b2d031948de143)
- [Fix] use `has-tostringtag` to behave correctly in the presence of symbol shams [`dc29e52`](https://github.com/inspect-js/is-arguments/commit/dc29e521d71da420414110919a1e0fde8ec6eba3)
- [Dev Deps] update `auto-changelog`, `eslint`, `tape` [`a966d25`](https://github.com/inspect-js/is-arguments/commit/a966d25535c5f050ca5ce43a1559f93698a7130b)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape` [`1218944`](https://github.com/inspect-js/is-arguments/commit/12189445a195558fdccebe099c699272d2082aa8)
- [meta] use `prepublishOnly` script for npm 7+ [`757dbee`](https://github.com/inspect-js/is-arguments/commit/757dbee3ec6f6225d4c7c91582e045cc1183dbd8)
- [Deps] update `call-bind` [`b206f05`](https://github.com/inspect-js/is-arguments/commit/b206f059571c430375c632e40dd29249fa76a8fd)
- [actions] update workflows [`b89b2f1`](https://github.com/inspect-js/is-arguments/commit/b89b2f1ab98bedebdf97d2397246030a1132c84e)

## [v1.1.0](https://github.com/inspect-js/is-arguments/compare/v1.0.4...v1.1.0) - 2020-12-04

### Commits

- [Tests] use shared travis-ci configs [`fd59a37`](https://github.com/inspect-js/is-arguments/commit/fd59a3779f004f36ea8e5ac90b0de9b97ff60755)
- [Tests] migrate tests to Github Actions [`982a0d6`](https://github.com/inspect-js/is-arguments/commit/982a0d68495b68e2b6ca8f4caa9f8a909ec56755)
- [Tests] remove `jscs` [`927d4b5`](https://github.com/inspect-js/is-arguments/commit/927d4b5c17b12c40f445491e52a11d5bed311ef6)
- [meta] add `auto-changelog` [`ef0634b`](https://github.com/inspect-js/is-arguments/commit/ef0634b0c07a12d9144c4db168cb79963326ec6d)
- [Tests] up to `node` `v12.10`, `v11.15`, `v10.16`, `v8.16`, `v6.17` [`1689f8b`](https://github.com/inspect-js/is-arguments/commit/1689f8bf533c8ab8cd95caf953905e3a204c0cdc)
- [Tests] up to `node` `v11.7`, `v10.15`, `v8.15`, `v6.16` [`145aaeb`](https://github.com/inspect-js/is-arguments/commit/145aaeb5a35e7abd3a8a5c9ec87c6e37f16ed068)
- [readme] fix repo URLs, remove defunct badges [`cc484a3`](https://github.com/inspect-js/is-arguments/commit/cc484a3ae787125eccc30a05c63b7ff6a1581591)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `tape` [`c888738`](https://github.com/inspect-js/is-arguments/commit/c888738ef1cf84b973169bbe6219b795de4acfc2)
- [Tests] run `nyc` on all tests [`0de8efb`](https://github.com/inspect-js/is-arguments/commit/0de8efb8091a3dd5708812cd26ad541f7dca773a)
- [actions] add automatic rebasing / merge commit blocking [`818775a`](https://github.com/inspect-js/is-arguments/commit/818775aa0c66064965517be554c3bcc57ec0d721)
- [Robustness] use `call-bind` [`31d0199`](https://github.com/inspect-js/is-arguments/commit/31d0199c1a560f113ff099a2f43068cdfe0af79e)
- [actions] add "Allow Edits" workflow [`0c55f7d`](https://github.com/inspect-js/is-arguments/commit/0c55f7d254ff335291d9cee39501b247f7248fb9)
- [meta] create FUNDING.yml [`ca7ed59`](https://github.com/inspect-js/is-arguments/commit/ca7ed597bac29790ac6233ff1bdff7704b870e96)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog` [`1ae5053`](https://github.com/inspect-js/is-arguments/commit/1ae505390efff099c50d0bc786a3ecc8d5303b04)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape`; add `safe-publish-latest` [`433f4a5`](https://github.com/inspect-js/is-arguments/commit/433f4a5573810fe689c5e56ad9fe69b6a2229b8c)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `covert`, `tape` [`78ea4e8`](https://github.com/inspect-js/is-arguments/commit/78ea4e8261bc326c1ae7e9e50bb655e8bf128c6b)
- [Tests] use `npm audit` instead of `nsp` [`07fb85b`](https://github.com/inspect-js/is-arguments/commit/07fb85bf396880648c2d4285273968d478df4711)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape` [`2204add`](https://github.com/inspect-js/is-arguments/commit/2204add22fcc15b1ee6aaae90578595b4f6d9647)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `safe-publish-latest` [`ce150c0`](https://github.com/inspect-js/is-arguments/commit/ce150c0c47504779ce812b1aefe044fcad1286af)
- [Tests] fix tests from 0de8efb [`ee45fc3`](https://github.com/inspect-js/is-arguments/commit/ee45fc387b655de6feac101c478af111d488e144)
- [Tests] use `npx aud` instead of `nsp` or `npm audit` with hoops [`03a312c`](https://github.com/inspect-js/is-arguments/commit/03a312cdae0aa058cfd094c996acb2af4e785484)
- [actions] switch Automatic Rebase workflow to `pull_request_target` event [`25d2ef8`](https://github.com/inspect-js/is-arguments/commit/25d2ef8da0b90c834d1fa6b83410205832e271d4)
- [Dev Deps] update `auto-changelog`, `tape` [`0fe60b7`](https://github.com/inspect-js/is-arguments/commit/0fe60b74b7f1254c386e14d0ea6d9cc074fdf12c)
- [Dev Deps] update `@ljharb/eslint-config`, `tape` [`4a9cbd0`](https://github.com/inspect-js/is-arguments/commit/4a9cbd0c91fd945ccc97c219d34e0840b0965586)
- [Dev Deps] update `auto-changelog`; add `aud` [`d9ff7d5`](https://github.com/inspect-js/is-arguments/commit/d9ff7d5f521eec5942019b1d7b38ace475da142f)
- [meta] add `funding` field [`adec2d2`](https://github.com/inspect-js/is-arguments/commit/adec2d293022ee3ec87479eaeae81bfec1ea1b18)
- [Tests] only audit prod deps [`f474960`](https://github.com/inspect-js/is-arguments/commit/f474960795eeb6087fc79eed8b787aa067b22ab1)

## [v1.0.4](https://github.com/inspect-js/is-arguments/compare/v1.0.3...v1.0.4) - 2018-11-05

### Commits

- [Fix] Fix errors about `in` operator. [`4d12e23`](https://github.com/inspect-js/is-arguments/commit/4d12e23fab8701207b7715fe7502db35c6edd3dd)

## [v1.0.3](https://github.com/inspect-js/is-arguments/compare/v1.0.2...v1.0.3) - 2018-11-02

### Fixed

- [Fix] add awareness of Symbol.toStringTag [`#20`](https://github.com/inspect-js/is-arguments/issues/20)

### Commits

- [Tests] up to `node` `v8.1`; `v7.10`, `v6.11`, `v4.8`; improve matrix; newer npm fails on older node [`ea5f23c`](https://github.com/inspect-js/is-arguments/commit/ea5f23c322234e18248b0acafe0f45333d5d78ec)
- [Tests] up to `node` `v9.1`, `v8.9`, `v6.12`; use `nvm install-latest-npm`; pin included builds to LTS. [`697a0a1`](https://github.com/inspect-js/is-arguments/commit/697a0a143d3b82f84956e4cca407c7eea228526b)
- [Tests] up to `node` `v10.0`, `v9.11`, `v8.11`, `v6.14`, `v4.9` [`40045c5`](https://github.com/inspect-js/is-arguments/commit/40045c5fe6ebb86f96125da96f7bfb9637579ce4)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `jscs`, `tape` [`08abc0d`](https://github.com/inspect-js/is-arguments/commit/08abc0d2e31c34514a58711f6203e41d06c3b81f)
- [Tests] up to `node` `v11.1`, `v10.13`, `v8.12` [`bf8d275`](https://github.com/inspect-js/is-arguments/commit/bf8d275ecf855c40c9c3f9c3ccf76874d4ce2497)
- [Tests] up to `node` `v7.0`, `v6.9`, `v4.6`; improve test matrix [`f813d86`](https://github.com/inspect-js/is-arguments/commit/f813d86b38f10d2e1f495597ca2a58d25a21339e)
- [Dev Deps] update `tape`, `jscs`, `eslint`, `@ljharb/eslint-config` [`e4f9aee`](https://github.com/inspect-js/is-arguments/commit/e4f9aee64f0f7f2f9d8132992b81d133b5d3c9c7)
- [Dev Deps] update `jscs`, `eslint`, `@ljharb/eslint-config` [`6c98d11`](https://github.com/inspect-js/is-arguments/commit/6c98d1171a043a4ab21d70b813379a4162ac3702)
- [Dev Deps] update `jscs`, `eslint`, `@ljharb/eslint-config` [`8e3178d`](https://github.com/inspect-js/is-arguments/commit/8e3178db172bc3ac889343aa3e38c24cc92aed08)
- package.json: use object form of "author", add "contributors" [`decc4fe`](https://github.com/inspect-js/is-arguments/commit/decc4feb9b31bd9f68b7a5f67aed39d32c9a6ab3)
- [Dev Deps] update `jscs`, `eslint`, `@ljharb/eslint-config` [`514902a`](https://github.com/inspect-js/is-arguments/commit/514902abde6b7d9397c9dbcd90f19fd78b70725a)
- [Tests] up to `node` `v5.6`, `v4.3` [`f11f47c`](https://github.com/inspect-js/is-arguments/commit/f11f47c5c1dae8adfda7ba1319de3b6e03db7925)
- [Dev Deps] add `npm run security` [`4adf82c`](https://github.com/inspect-js/is-arguments/commit/4adf82c0c9259eb81db18848a314b36db1c48d36)
- [Dev Deps] update `tape`, `jscs`, `eslint`, `@ljharb/eslint-config` [`f587aeb`](https://github.com/inspect-js/is-arguments/commit/f587aeb3ec04f2d22c2a8fd7686a2153d0fd50d2)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `nsp`, `tape` [`4f587bb`](https://github.com/inspect-js/is-arguments/commit/4f587bb7a2c499b1aa2e2aea60da8c0ee91c9df2)
- [Tests] up to `node` `v6.2`, `v5.11` [`36939c5`](https://github.com/inspect-js/is-arguments/commit/36939c5e1d8ce56c356a3f2144983839d86b3ae8)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `nsp`, `tape` [`d779cc8`](https://github.com/inspect-js/is-arguments/commit/d779cc875bd6fa15d861a134065d629159051331)
- Only apps should have lockfiles [`f50ce65`](https://github.com/inspect-js/is-arguments/commit/f50ce65fe94728b6f127a0c11f2efc6473f56cf3)
- [Dev Deps] update `tape`, `jscs`, `eslint`, `@ljharb/eslint-config` [`3025559`](https://github.com/inspect-js/is-arguments/commit/30255597cf578068e5a28d7a6e29076355132c87)
- [Dev Deps] update `tape`, `jscs`, `eslint`, `@ljharb/eslint-config` [`3b9ddee`](https://github.com/inspect-js/is-arguments/commit/3b9ddeef740608d84d2b825b9a90e4adf049c905)
- [Tests] up to `v5.8`, `v4.4` [`d4902cf`](https://github.com/inspect-js/is-arguments/commit/d4902cfb07e0bfaa0788a7847fcaaba91c8e3435)
- [Tests] fix npm upgrades for older nodes [`c617dd3`](https://github.com/inspect-js/is-arguments/commit/c617dd3a7b6a70162cbeb985009620acd69c029d)
- [Tests] up to `node` `v5.3` [`cdd2a61`](https://github.com/inspect-js/is-arguments/commit/cdd2a617c3d1810149683596fe90024ae9dcc549)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape` [`7719172`](https://github.com/inspect-js/is-arguments/commit/77191721ef92ce9dc72fdae8e1cfdc2ec74317d9)
- [Dev Deps] update `es5-shim`, `tape`, `nsp`, `eslint` [`6a5f82b`](https://github.com/inspect-js/is-arguments/commit/6a5f82bc6d9407e64fc4c7794d546e4db8ab61ed)
- [Dev Deps] update `tape`, `eslint`, `@ljharb/eslint-config` [`c896c1c`](https://github.com/inspect-js/is-arguments/commit/c896c1c4629ff3c8cda25e0c3e607d2950ff4ba9)
- [Tests] Use `pretest` for running the linter. [`83db117`](https://github.com/inspect-js/is-arguments/commit/83db1173fde93c2d8a490663ce850321b018eab2)
- [Dev Deps] update `@ljharb/eslint-config`, `eslint` [`57fdc63`](https://github.com/inspect-js/is-arguments/commit/57fdc636dea2f5c641e2d0c0dfbe0ac589acb69a)
- [Tests] up to `node` `v7.2` [`aa3eacf`](https://github.com/inspect-js/is-arguments/commit/aa3eacf27001a92aab4874f7d29f693ed6221b4a)
- [Tests] up to `node` `v5.10` [`94ff6d7`](https://github.com/inspect-js/is-arguments/commit/94ff6d72c095cae00a4df06043976dc3e414f49b)
- [Tests] on `node` `v4.2` [`cdb1fb5`](https://github.com/inspect-js/is-arguments/commit/cdb1fb5babe08c845570cbae218c0b96753c1152)

## [v1.0.2](https://github.com/inspect-js/is-arguments/compare/v1.0.1...v1.0.2) - 2015-09-21

### Commits

- Update `eslint`, use my personal shared config. [`8e211f4`](https://github.com/inspect-js/is-arguments/commit/8e211f46b17ae8d89aa5484b4b3b853d3d1b3fa9)
- In modern engines, only export the "is standard arguments" check. [`e8aa23f`](https://github.com/inspect-js/is-arguments/commit/e8aa23fc19f6d1c3c952174391a4903d90fcd622)
- Update `jscs`, `eslint`, `@ljharb/eslint-config` [`8a90bca`](https://github.com/inspect-js/is-arguments/commit/8a90bcad88025736a7c127123f1473af35bae6f7)
- Update `eslint` [`2214b5d`](https://github.com/inspect-js/is-arguments/commit/2214b5dac911e1eb949179f9034aa37ba7c079d7)
- Update `eslint` [`ca97c5b`](https://github.com/inspect-js/is-arguments/commit/ca97c5b22e7cf4f30d90ee1519988ecd4bf36887)
- [Dev Deps] update `jscs` [`ca6a477`](https://github.com/inspect-js/is-arguments/commit/ca6a477c16c70c0e5f29d56713237703ab610fdf)
- Update `covert`, `jscs`, `eslint` [`232d92a`](https://github.com/inspect-js/is-arguments/commit/232d92ab1dff7b0ad64024726cda437b32ce1906)
- [Tests] up to `io.js` `v3.3`, `node` `v4.1` [`460d700`](https://github.com/inspect-js/is-arguments/commit/460d700bdb5d8b261995e3d8f3e6b3eda4f91bcf)
- Test up to `io.js` `v2.3` [`7ef2293`](https://github.com/inspect-js/is-arguments/commit/7ef229388819ae1f1c1d55dbe741c90977cc3a3f)
- [Dev Deps] update `tape`, `jscs`, `eslint`, `@ljharb/eslint-config` [`29f3d71`](https://github.com/inspect-js/is-arguments/commit/29f3d71eb516326409bd24bc7e6d4ebb6a872d01)
- [Dev Deps] update `tape`, `eslint`, `@ljharb/eslint-config` [`1c79a85`](https://github.com/inspect-js/is-arguments/commit/1c79a85d670d8dc5dbb1831ee0de0c8858a94775)
- `toString` as a variable name breaks in some older browsers. [`1e59f2b`](https://github.com/inspect-js/is-arguments/commit/1e59f2bf79454188145de5275a64996eafc94420)
- Update `tape`, `eslint` [`1efbefd`](https://github.com/inspect-js/is-arguments/commit/1efbefd84df6ae802245ebe6371cd15255ee23e7)
- Test up to `io.js` `v2.5` [`0760acc`](https://github.com/inspect-js/is-arguments/commit/0760acc3139d1930efebc4321c1f96ba1406e2de)
- Test up to `io.js` `v2.1` [`4c2245f`](https://github.com/inspect-js/is-arguments/commit/4c2245f3deccdb3ec70e4f79e5e8aac697f35d08)
- [Dev Deps] update `tape` [`348980e`](https://github.com/inspect-js/is-arguments/commit/348980e1666b66724e37c9df63d18d540a51d5fe)
- Switch from vb.teelaun.ch to versionbadg.es for the npm version badge SVG. [`91d8c4f`](https://github.com/inspect-js/is-arguments/commit/91d8c4fd4516aae483fa2dd9c4a5b44c48e773f0)
- Update `tape` [`ec9b92a`](https://github.com/inspect-js/is-arguments/commit/ec9b92a244f7a077fe1df58af89f56a39d4d2600)
- Update `tape` [`6bc8969`](https://github.com/inspect-js/is-arguments/commit/6bc8969c40b2b2cd1c767933b7ef3d8ff65bf67f)
- Test on `io.js` `v3.0` [`33d9578`](https://github.com/inspect-js/is-arguments/commit/33d957814d515855583e98e652624e5920cc9496)

## [v1.0.1](https://github.com/inspect-js/is-arguments/compare/v1.0.0...v1.0.1) - 2015-04-29

### Commits

- Update `jscs`, add `npm run eslint` [`13a5f01`](https://github.com/inspect-js/is-arguments/commit/13a5f015aa67cb2402b5fdb21c68180ff7036a14)
- Using my standard jscs.json file [`d669fc4`](https://github.com/inspect-js/is-arguments/commit/d669fc49c0db56457eb55a77a2f9c40916ad6361)
- Adding `npm run lint` [`ece5d05`](https://github.com/inspect-js/is-arguments/commit/ece5d0581fadcff99b181d67b54eacb4869cfb98)
- Test on latest `io.js` [`908b092`](https://github.com/inspect-js/is-arguments/commit/908b092912fa9c89c20a41fdd117b21e857bfc84)
- Adding license and downloads badges [`05fd28b`](https://github.com/inspect-js/is-arguments/commit/05fd28b28d857ecb2220d0ac6267f36ec1e65eae)
- Make sure old and unstable nodes don't break Travis [`16ee7ea`](https://github.com/inspect-js/is-arguments/commit/16ee7eae70015864a8b3f2fe03463ecb4d707451)
- All grade A-supported `node`/`iojs` versions now ship with an `npm` that understands `^`. [`9846c79`](https://github.com/inspect-js/is-arguments/commit/9846c79d1999432538ea757c0dbf61ab3f0d54cd)
- Run `travis-ci` tests on `iojs` and `node` v0.12; speed up builds; allow 0.8 failures. [`27c014d`](https://github.com/inspect-js/is-arguments/commit/27c014d217d41d33b0bb1735e38184b871d86311)
- Use SVG instead of PNG badges [`ea01e68`](https://github.com/inspect-js/is-arguments/commit/ea01e68896722351c63912e37cc37541f9b78780)
- Remove unused links in README [`f5baaff`](https://github.com/inspect-js/is-arguments/commit/f5baaff1931bb9d48b20270a94d99121a3176dba)
- Test on latest `io.js` versions [`293e2c4`](https://github.com/inspect-js/is-arguments/commit/293e2c4d1edfdf9c6db88663314599ecde08a945)
- Update `tape`, `jscs` [`d72ab08`](https://github.com/inspect-js/is-arguments/commit/d72ab08147ab5256e1efd61c01b719796699faf0)
- Update `jscs` [`5f6e6d4`](https://github.com/inspect-js/is-arguments/commit/5f6e6d42645845b5663b5fef716e2963686aad8d)
- Update `tape`, `jscs` [`39ae55b`](https://github.com/inspect-js/is-arguments/commit/39ae55b6ef0c3d6206116bd7500bc601485d8698)
- Update `tape`, `jscs` [`594d928`](https://github.com/inspect-js/is-arguments/commit/594d92852cf7a4d93c8ff5ac157fdae9dbefc133)
- Updating dependencies [`183ac15`](https://github.com/inspect-js/is-arguments/commit/183ac151d27032fce4aaf3fa095cce9b086eb651)
- Update `tape` [`77b9cea`](https://github.com/inspect-js/is-arguments/commit/77b9ceae8dcb2c2e73d85f512d0d0309427c4011)
- Lock covert to v1.0.0. [`28d9052`](https://github.com/inspect-js/is-arguments/commit/28d9052eaa99f36ca5c61f35645b5e14ddf6f8f9)
- Updating tape [`d9ee2ac`](https://github.com/inspect-js/is-arguments/commit/d9ee2ac24045fa81ffed356410cfc2d878bc8b4b)
- Updating jscs [`c0cab8f`](https://github.com/inspect-js/is-arguments/commit/c0cab8fd6c0509153142a3cc79a7a4dceba322be)
- Updating jscs [`c59352a`](https://github.com/inspect-js/is-arguments/commit/c59352ae99c0d813168c19b9c888182ea11ae17a)
- Run linter as part of tests [`8b8154e`](https://github.com/inspect-js/is-arguments/commit/8b8154ef5b2566250baed70807affdbba93c3bcf)
- Oops, properly running code coverage checks during tests. [`cc441d0`](https://github.com/inspect-js/is-arguments/commit/cc441d0c488486c688e513f7d129f4f8ea2ee323)
- Updating covert. [`142db90`](https://github.com/inspect-js/is-arguments/commit/142db90aa3448995232c419419523b67a953b012)
- Updating tape [`265fd0f`](https://github.com/inspect-js/is-arguments/commit/265fd0ff3ee71ab8aa3d2d90be74066c1aa7c9c0)
- Updating tape [`7e9aec6`](https://github.com/inspect-js/is-arguments/commit/7e9aec654b8f5fe0bb2f8c940c84da8ec29a2102)
- Updating covert [`d96860a`](https://github.com/inspect-js/is-arguments/commit/d96860ab520ae62a37e80ad259b936ace09954d9)
- Updating tape [`1ec32a0`](https://github.com/inspect-js/is-arguments/commit/1ec32a0c02f53192a94521d63d4cf8fbb567fc84)
- Run code coverage as part of tests [`155ab22`](https://github.com/inspect-js/is-arguments/commit/155ab227902287c5eec653c25bec2cc4b530e635)
- Coverage does not work currently on node 0.6. [`9acf696`](https://github.com/inspect-js/is-arguments/commit/9acf696a1f3e202990fea494615377b40a380b79)
- Testing node 0.6 again [`a23ca07`](https://github.com/inspect-js/is-arguments/commit/a23ca07427cf3801b82e6a93d9a8904f392f4b20)

## [v1.0.0](https://github.com/inspect-js/is-arguments/compare/v0.1.0...v1.0.0) - 2014-01-07

### Commits

- :metal: 1.0.0 [`fc08874`](https://github.com/inspect-js/is-arguments/commit/fc08874107ac74fca91bd678843dbf7ea3a4c54a)

## v0.1.0 - 2014-01-07

### Commits

- package.json [`c547055`](https://github.com/inspect-js/is-arguments/commit/c54705585e907f1ef22473bff656904e49d22db2)
- read me! [`72ed639`](https://github.com/inspect-js/is-arguments/commit/72ed639c5db9c8732073ec1d30f14b493bb976da)
- Initial commit [`d2e0264`](https://github.com/inspect-js/is-arguments/commit/d2e0264bed7948b6c4f7bfde12ab351bffbf4cc1)
- Tests. [`032ed16`](https://github.com/inspect-js/is-arguments/commit/032ed16abf54466a75333e1da2ceef780fb58c1e)
- Implementation. [`71c312d`](https://github.com/inspect-js/is-arguments/commit/71c312d76d3b4b99cf7a1e0b442e9492069fba2b)
- Travis CI [`5500a66`](https://github.com/inspect-js/is-arguments/commit/5500a664de6b4484850f02b74641baa6e7d74c50)
- Running code coverage as part of tests. [`88ad34a`](https://github.com/inspect-js/is-arguments/commit/88ad34a133188cea28aa26a9f583653ea3a0260e)
