import { formatDistance } from "./sr-Latn/_lib/formatDistance.js";
import { formatLong } from "./sr-Latn/_lib/formatLong.js";
import { formatRelative } from "./sr-Latn/_lib/formatRelative.js";
import { localize } from "./sr-Latn/_lib/localize.js";
import { match } from "./sr-Latn/_lib/match.js";

/**
 * @category Locales
 * @summary Serbian latin locale.
 * @language Serbian
 * @iso-639-2 srp
 * <AUTHOR> [@rogyvoje](https://github.com/rogyvoje)
 */
export const srLatn = {
  code: "sr-Latn",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1,
  },
};

// Fallback for modularized imports:
export default srLatn;
