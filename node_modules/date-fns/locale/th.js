import { formatDistance } from "./th/_lib/formatDistance.js";
import { formatLong } from "./th/_lib/formatLong.js";
import { formatRelative } from "./th/_lib/formatRelative.js";
import { localize } from "./th/_lib/localize.js";
import { match } from "./th/_lib/match.js";

/**
 * @category Locales
 * @summary Thai locale.
 * @language Thai
 * @iso-639-2 tha
 * <AUTHOR> <PERSON> [@athivvat](https://github.com/athivvat)
 * <AUTHOR>
 * <AUTHOR> I. [@nodtem66](https://github.com/nodtem66)
 */
export const th = {
  code: "th",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 0 /* Sunday */,
    firstWeekContainsDate: 1,
  },
};

// Fallback for modularized imports:
export default th;
