import { formatDistance } from "./te/_lib/formatDistance.js";
import { formatLong } from "./te/_lib/formatLong.js";
import { formatRelative } from "./te/_lib/formatRelative.js";
import { localize } from "./te/_lib/localize.js";
import { match } from "./te/_lib/match.js";

/**
 * @category Locales
 * @summary Telugu locale
 * @language Telugu
 * @iso-639-2 tel
 * <AUTHOR> [@kranthilakum](https://github.com/kranthilakum)
 */
export const te = {
  code: "te",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 0 /* Sunday */,
    firstWeekContainsDate: 1,
  },
};

// Fallback for modularized imports:
export default te;
