import { jsx as _jsx } from "react/jsx-runtime";
import * as React from "react";
import { cn } from "@flowiq/shared-utils";
const Toast = React.forwardRef(({ className, variant = "default", ...props }, ref) => {
    return (_jsx("div", { ref: ref, className: cn("fixed bottom-4 right-4 p-4 rounded-md shadow-lg z-50", {
            "bg-white border": variant === "default",
            "bg-red-500 text-white": variant === "destructive",
            "bg-green-500 text-white": variant === "success",
        }, className), ...props }));
});
Toast.displayName = "Toast";
export { Toast };
