import { jsx as _jsx } from "react/jsx-runtime";
import * as React from "react";
import { cn } from "@flowiq/shared-utils";
const Separator = React.forwardRef(({ className, orientation = "horizontal", ...props }, ref) => (_jsx("div", { ref: ref, className: cn("shrink-0 bg-border", orientation === "horizontal" ? "h-[1px] w-full" : "h-full w-[1px]", className), ...props })));
Separator.displayName = "Separator";
export { Separator };
