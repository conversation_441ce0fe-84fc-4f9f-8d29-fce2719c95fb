import { jsx as _jsx } from "react/jsx-runtime";
import * as React from "react";
import { cn } from "@flowiq/shared-utils";
const DropdownMenu = React.forwardRef(({ className, children, open = false, ...props }, ref) => {
    return (_jsx("div", { ref: ref, className: cn("relative", className), ...props, children: children }));
});
DropdownMenu.displayName = "DropdownMenu";
const DropdownMenuTrigger = React.forwardRef(({ className, ...props }, ref) => (_jsx("button", { ref: ref, className: cn("inline-flex items-center justify-center", className), ...props })));
DropdownMenuTrigger.displayName = "DropdownMenuTrigger";
const DropdownMenuContent = React.forwardRef(({ className, ...props }, ref) => (_jsx("div", { ref: ref, className: cn("absolute top-full left-0 mt-1 bg-white border rounded-md shadow-lg z-50", className), ...props })));
DropdownMenuContent.displayName = "DropdownMenuContent";
const DropdownMenuItem = React.forwardRef(({ className, ...props }, ref) => (_jsx("div", { ref: ref, className: cn("px-3 py-2 hover:bg-gray-100 cursor-pointer", className), ...props })));
DropdownMenuItem.displayName = "DropdownMenuItem";
export { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem };
