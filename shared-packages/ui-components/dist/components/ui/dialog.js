import { jsx as _jsx } from "react/jsx-runtime";
import * as React from "react";
import { cn } from "@flowiq/shared-utils";
const Dialog = React.forwardRef(({ className, children, open = false, ...props }, ref) => {
    if (!open)
        return null;
    return (_jsx("div", { ref: ref, className: cn("fixed inset-0 z-50 flex items-center justify-center bg-black/50", className), ...props, children: _jsx("div", { className: "bg-white rounded-lg p-6 max-w-md w-full mx-4", children: children }) }));
});
Dialog.displayName = "Dialog";
const DialogContent = React.forwardRef(({ className, ...props }, ref) => (_jsx("div", { ref: ref, className: cn("space-y-4", className), ...props })));
DialogContent.displayName = "DialogContent";
const DialogHeader = React.forwardRef(({ className, ...props }, ref) => (_jsx("div", { ref: ref, className: cn("space-y-2", className), ...props })));
DialogHeader.displayName = "DialogHeader";
const DialogTitle = React.forwardRef(({ className, ...props }, ref) => (_jsx("h2", { ref: ref, className: cn("text-lg font-semibold", className), ...props })));
DialogTitle.displayName = "DialogTitle";
export { Dialog, DialogContent, DialogHeader, DialogTitle };
