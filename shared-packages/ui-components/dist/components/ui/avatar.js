import { jsx as _jsx } from "react/jsx-runtime";
import * as React from "react";
import { cn } from "@flowiq/shared-utils";
const Avatar = React.forwardRef(({ className, ...props }, ref) => (_jsx("div", { ref: ref, className: cn("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full", className), ...props })));
Avatar.displayName = "Avatar";
const AvatarImage = React.forwardRef(({ className, ...props }, ref) => (_jsx("img", { ref: ref, className: cn("aspect-square h-full w-full", className), ...props })));
AvatarImage.displayName = "AvatarImage";
const AvatarFallback = React.forwardRef(({ className, ...props }, ref) => (_jsx("div", { ref: ref, className: cn("flex h-full w-full items-center justify-center rounded-full bg-muted", className), ...props })));
AvatarFallback.displayName = "AvatarFallback";
export { Avatar, AvatarImage, AvatarFallback };
