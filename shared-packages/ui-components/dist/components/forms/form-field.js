import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import * as React from "react";
import { cn } from "@flowiq/shared-utils";
const FormField = React.forwardRef(({ className, label, error, required, children, ...props }, ref) => {
    return (_jsxs("div", { ref: ref, className: cn("space-y-2", className), ...props, children: [label && (_jsxs("label", { className: "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70", children: [label, required && _jsx("span", { className: "text-red-500 ml-1", children: "*" })] })), children, error && (_jsx("p", { className: "text-sm text-red-500", children: error }))] }));
});
FormField.displayName = "FormField";
export { FormField };
