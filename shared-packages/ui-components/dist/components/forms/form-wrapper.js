import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import * as React from "react";
import { cn } from "@flowiq/shared-utils";
const FormWrapper = React.forwardRef(({ className, children, title, description, ...props }, ref) => {
    return (_jsxs("form", { ref: ref, className: cn("space-y-6", className), ...props, children: [(title || description) && (_jsxs("div", { className: "space-y-2", children: [title && (_jsx("h2", { className: "text-2xl font-bold tracking-tight", children: title })), description && (_jsx("p", { className: "text-muted-foreground", children: description }))] })), children] }));
});
FormWrapper.displayName = "FormWrapper";
export { FormWrapper };
