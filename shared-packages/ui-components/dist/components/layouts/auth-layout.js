import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import * as React from "react";
import { cn } from "@flowiq/shared-utils";
const AuthLayout = React.forwardRef(({ className, children, ...props }, ref) => {
    return (_jsx("div", { ref: ref, className: cn("min-h-screen flex items-center justify-center bg-background", className), ...props, children: _jsxs("div", { className: "w-full max-w-md space-y-6", children: [_jsx("div", { className: "text-center", children: _jsx("h1", { className: "text-2xl font-bold", children: "FlowIQ" }) }), children] }) }));
});
AuthLayout.displayName = "AuthLayout";
export { AuthLayout };
