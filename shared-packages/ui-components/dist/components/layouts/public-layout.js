import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import * as React from "react";
import { cn } from "@flowiq/shared-utils";
const PublicLayout = React.forwardRef(({ className, children, ...props }, ref) => {
    return (_jsxs("div", { ref: ref, className: cn("min-h-screen bg-background", className), ...props, children: [_jsx("header", { className: "border-b", children: _jsx("div", { className: "container mx-auto px-4 py-4", children: _jsxs("nav", { className: "flex items-center justify-between", children: [_jsx("h1", { className: "text-xl font-bold", children: "FlowIQ" }), _jsxs("div", { className: "space-x-4", children: [_jsx("a", { href: "#", className: "text-sm hover:underline", children: "Features" }), _jsx("a", { href: "#", className: "text-sm hover:underline", children: "Pricing" }), _jsx("a", { href: "#", className: "text-sm hover:underline", children: "Contact" })] })] }) }) }), _jsx("main", { children: children }), _jsx("footer", { className: "border-t mt-auto", children: _jsx("div", { className: "container mx-auto px-4 py-6", children: _jsx("p", { className: "text-center text-sm text-muted-foreground", children: "\u00A9 2024 FlowIQ. All rights reserved." }) }) })] }));
});
PublicLayout.displayName = "PublicLayout";
export { PublicLayout };
