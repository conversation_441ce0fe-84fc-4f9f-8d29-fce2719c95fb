import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import * as React from "react";
import { cn } from "@flowiq/shared-utils";
const DashboardLayout = React.forwardRef(({ className, children, ...props }, ref) => {
    return (_jsx("div", { ref: ref, className: cn("min-h-screen bg-background", className), ...props, children: _jsxs("div", { className: "flex", children: [_jsx("aside", { className: "w-64 bg-card border-r", children: _jsx("div", { className: "p-4", children: _jsx("h2", { className: "text-lg font-semibold", children: "FlowIQ" }) }) }), _jsx("main", { className: "flex-1", children: _jsx("div", { className: "p-6", children: children }) })] }) }));
});
DashboardLayout.displayName = "DashboardLayout";
export { DashboardLayout };
