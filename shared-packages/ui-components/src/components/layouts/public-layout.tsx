import * as React from "react"
import { cn } from "@flowiq/shared-utils"

export interface PublicLayoutProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
}

const PublicLayout = React.forwardRef<HTMLDivElement, PublicLayoutProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn("min-h-screen bg-background", className)}
        {...props}
      >
        {/* Header */}
        <header className="border-b">
          <div className="container mx-auto px-4 py-4">
            <nav className="flex items-center justify-between">
              <h1 className="text-xl font-bold">FlowIQ</h1>
              <div className="space-x-4">
                <a href="#" className="text-sm hover:underline">Features</a>
                <a href="#" className="text-sm hover:underline">Pricing</a>
                <a href="#" className="text-sm hover:underline">Contact</a>
              </div>
            </nav>
          </div>
        </header>
        
        {/* Main content */}
        <main>
          {children}
        </main>
        
        {/* Footer */}
        <footer className="border-t mt-auto">
          <div className="container mx-auto px-4 py-6">
            <p className="text-center text-sm text-muted-foreground">
              © 2024 FlowIQ. All rights reserved.
            </p>
          </div>
        </footer>
      </div>
    )
  }
)
PublicLayout.displayName = "PublicLayout"

export { PublicLayout }
