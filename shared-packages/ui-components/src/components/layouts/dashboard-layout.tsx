import * as React from "react"
import { cn } from "@flowiq/shared-utils"

export interface DashboardLayoutProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
}

const DashboardLayout = React.forwardRef<HTMLDivElement, DashboardLayoutProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn("min-h-screen bg-background", className)}
        {...props}
      >
        <div className="flex">
          {/* Sidebar placeholder */}
          <aside className="w-64 bg-card border-r">
            <div className="p-4">
              <h2 className="text-lg font-semibold">FlowIQ</h2>
            </div>
          </aside>
          
          {/* Main content */}
          <main className="flex-1">
            <div className="p-6">
              {children}
            </div>
          </main>
        </div>
      </div>
    )
  }
)
DashboardLayout.displayName = "DashboardLayout"

export { DashboardLayout }
