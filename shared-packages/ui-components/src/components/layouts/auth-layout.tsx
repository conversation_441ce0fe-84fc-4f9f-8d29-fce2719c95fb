import * as React from "react"
import { cn } from "@flowiq/shared-utils"

export interface AuthLayoutProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
}

const AuthLayout = React.forwardRef<HTMLDivElement, AuthLayoutProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn("min-h-screen flex items-center justify-center bg-background", className)}
        {...props}
      >
        <div className="w-full max-w-md space-y-6">
          <div className="text-center">
            <h1 className="text-2xl font-bold">FlowIQ</h1>
          </div>
          {children}
        </div>
      </div>
    )
  }
)
AuthLayout.displayName = "AuthLayout"

export { AuthLayout }
