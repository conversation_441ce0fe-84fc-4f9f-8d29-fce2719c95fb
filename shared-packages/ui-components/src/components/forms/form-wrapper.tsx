import * as React from "react"
import { cn } from "@flowiq/shared-utils"

export interface FormWrapperProps extends React.FormHTMLAttributes<HTMLFormElement> {
  children: React.ReactNode
  title?: string
  description?: string
}

const FormWrapper = React.forwardRef<HTMLFormElement, FormWrapperProps>(
  ({ className, children, title, description, ...props }, ref) => {
    return (
      <form ref={ref} className={cn("space-y-6", className)} {...props}>
        {(title || description) && (
          <div className="space-y-2">
            {title && (
              <h2 className="text-2xl font-bold tracking-tight">{title}</h2>
            )}
            {description && (
              <p className="text-muted-foreground">{description}</p>
            )}
          </div>
        )}
        {children}
      </form>
    )
  }
)
FormWrapper.displayName = "FormWrapper"

export { FormWrapper }
