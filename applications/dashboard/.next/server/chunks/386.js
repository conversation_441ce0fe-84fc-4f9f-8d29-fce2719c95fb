exports.id=386,exports.ids=[386],exports.modules={2945:()=>{},7956:(e,r,s)=>{Promise.resolve().then(s.t.bind(s,3724,23)),Promise.resolve().then(s.t.bind(s,5365,23)),Promise.resolve().then(s.t.bind(s,4900,23)),Promise.resolve().then(s.t.bind(s,4714,23)),Promise.resolve().then(s.t.bind(s,5392,23)),Promise.resolve().then(s.t.bind(s,8898,23))},5345:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>RootLayout,metadata:()=>i});var a=s(4656),t=s(1326),n=s.n(t);s(7272);var l=s(8875);let i={title:"FlowIQ Dashboard - Business Management Platform",description:"Comprehensive business management dashboard for South African businesses"};function RootLayout({children:e}){return a.jsx("html",{lang:"en",children:a.jsx("body",{className:n().className,children:a.jsx(l.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:e})})})}},4903:(e,r,s)=>{"use strict";s.d(r,{cn:()=>cn,xG:()=>formatCurrency});var a=s(6874),t=s(9222);function cn(...e){return(0,t.m)((0,a.W)(e))}var n=s(2858);function formatCurrency(e,r="ZAR"){let s={ZAR:new Intl.NumberFormat("en-ZA",{style:"currency",currency:"ZAR"}),USD:new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}),EUR:new Intl.NumberFormat("en-EU",{style:"currency",currency:"EUR"})};return s[r].format(e)}n.z.string().refine(e=>{if(13!==e.length||!/^\d{13}$/.test(e))return!1;let r=e.split("").map(Number),s=0;for(let e=0;e<12;e++)if(e%2==0)s+=r[e];else{let a=2*r[e];s+=a>9?a-9:a}let a=(10-s%10)%10;return a===r[12]},"Invalid South African ID number"),n.z.string().refine(e=>/^4\d{9}$/.test(e),"Invalid VAT number format"),n.z.string().refine(e=>/^\d{4}\/\d{6}\/\d{2}$/.test(e),"Invalid company registration format"),n.z.string().min(8,"Password must be at least 8 characters").regex(/[A-Z]/,"Password must contain at least one uppercase letter").regex(/[a-z]/,"Password must contain at least one lowercase letter").regex(/\d/,"Password must contain at least one number").regex(/[^A-Za-z0-9]/,"Password must contain at least one special character"),n.z.string().email("Invalid email address"),n.z.string().refine(e=>/^(\+27|0)[1-9]\d{8}$/.test(e),"Invalid South African phone number")},8875:(e,r,s)=>{"use strict";s.d(r,{zx:()=>l,Zb:()=>i,aY:()=>f,Ol:()=>d,ll:()=>o});var a=s(4656),t=s(3542),n=s(4903);let l=t.forwardRef(({className:e,variant:r="default",size:s="default",...t},l)=>a.jsx("button",{className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{"bg-primary text-primary-foreground hover:bg-primary/90":"default"===r,"bg-destructive text-destructive-foreground hover:bg-destructive/90":"destructive"===r,"border border-input bg-background hover:bg-accent hover:text-accent-foreground":"outline"===r,"bg-secondary text-secondary-foreground hover:bg-secondary/80":"secondary"===r,"hover:bg-accent hover:text-accent-foreground":"ghost"===r,"text-primary underline-offset-4 hover:underline":"link"===r},{"h-10 px-4 py-2":"default"===s,"h-9 rounded-md px-3":"sm"===s,"h-11 rounded-md px-8":"lg"===s,"h-10 w-10":"icon"===s},e),ref:l,...t}));l.displayName="Button";let i=t.forwardRef(({className:e,...r},s)=>a.jsx("div",{ref:s,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));i.displayName="Card";let d=t.forwardRef(({className:e,...r},s)=>a.jsx("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...r}));d.displayName="CardHeader";let o=t.forwardRef(({className:e,...r},s)=>a.jsx("h3",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));o.displayName="CardTitle";let c=t.forwardRef(({className:e,...r},s)=>a.jsx("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",e),...r}));c.displayName="CardDescription";let f=t.forwardRef(({className:e,...r},s)=>a.jsx("div",{ref:s,className:(0,n.cn)("p-6 pt-0",e),...r}));f.displayName="CardContent";let m=t.forwardRef(({className:e,...r},s)=>a.jsx("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",e),...r}));m.displayName="CardFooter";let u=t.forwardRef(({className:e,type:r,...s},t)=>a.jsx("input",{type:r,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:t,...s}));u.displayName="Input";let x=t.forwardRef(({className:e,...r},s)=>a.jsx("label",{ref:s,className:(0,n.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",e),...r}));x.displayName="Label";let b=t.forwardRef(({className:e,children:r,...s},t)=>a.jsx("select",{className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:t,...s,children:r}));b.displayName="Select";let g=t.forwardRef(({className:e,...r},s)=>a.jsx("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:s,...r}));g.displayName="Textarea";let p=t.forwardRef(({className:e,children:r,open:s=!1,...t},l)=>s?a.jsx("div",{ref:l,className:(0,n.cn)("fixed inset-0 z-50 flex items-center justify-center bg-black/50",e),...t,children:a.jsx("div",{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4",children:r})}):null);p.displayName="Dialog";let h=t.forwardRef(({className:e,...r},s)=>a.jsx("div",{ref:s,className:(0,n.cn)("space-y-4",e),...r}));h.displayName="DialogContent";let v=t.forwardRef(({className:e,...r},s)=>a.jsx("div",{ref:s,className:(0,n.cn)("space-y-2",e),...r}));v.displayName="DialogHeader";let y=t.forwardRef(({className:e,...r},s)=>a.jsx("h2",{ref:s,className:(0,n.cn)("text-lg font-semibold",e),...r}));y.displayName="DialogTitle";let N=t.forwardRef(({className:e,children:r,open:s=!1,...t},l)=>a.jsx("div",{ref:l,className:(0,n.cn)("relative",e),...t,children:r}));N.displayName="DropdownMenu";let w=t.forwardRef(({className:e,...r},s)=>a.jsx("button",{ref:s,className:(0,n.cn)("inline-flex items-center justify-center",e),...r}));w.displayName="DropdownMenuTrigger";let j=t.forwardRef(({className:e,...r},s)=>a.jsx("div",{ref:s,className:(0,n.cn)("absolute top-full left-0 mt-1 bg-white border rounded-md shadow-lg z-50",e),...r}));j.displayName="DropdownMenuContent";let R=t.forwardRef(({className:e,...r},s)=>a.jsx("div",{ref:s,className:(0,n.cn)("px-3 py-2 hover:bg-gray-100 cursor-pointer",e),...r}));R.displayName="DropdownMenuItem";let k=t.forwardRef(({className:e,variant:r="default",...s},t)=>a.jsx("div",{ref:t,className:(0,n.cn)("fixed bottom-4 right-4 p-4 rounded-md shadow-lg z-50",{"bg-white border":"default"===r,"bg-red-500 text-white":"destructive"===r,"bg-green-500 text-white":"success"===r},e),...s}));k.displayName="Toast";let I=t.forwardRef(({className:e,...r},s)=>a.jsx("div",{ref:s,className:(0,n.cn)("w-full",e),...r}));I.displayName="Tabs";let A=t.forwardRef(({className:e,...r},s)=>a.jsx("div",{ref:s,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...r}));A.displayName="TabsList";let C=t.forwardRef(({className:e,...r},s)=>a.jsx("button",{ref:s,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...r}));C.displayName="TabsTrigger";let P=t.forwardRef(({className:e,...r},s)=>a.jsx("div",{ref:s,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...r}));P.displayName="TabsContent";let z=t.forwardRef(({className:e,variant:r="default",...s},t)=>a.jsx("div",{ref:t,className:(0,n.cn)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{"border-transparent bg-primary text-primary-foreground hover:bg-primary/80":"default"===r,"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80":"secondary"===r,"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80":"destructive"===r,"text-foreground":"outline"===r},e),...s}));z.displayName="Badge";let D=t.forwardRef(({className:e,...r},s)=>a.jsx("div",{ref:s,className:(0,n.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",e),...r}));D.displayName="Avatar";let F=t.forwardRef(({className:e,...r},s)=>a.jsx("img",{ref:s,className:(0,n.cn)("aspect-square h-full w-full",e),...r}));F.displayName="AvatarImage";let T=t.forwardRef(({className:e,...r},s)=>a.jsx("div",{ref:s,className:(0,n.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",e),...r}));T.displayName="AvatarFallback";let S=t.forwardRef(({className:e,orientation:r="horizontal",...s},t)=>a.jsx("div",{ref:t,className:(0,n.cn)("shrink-0 bg-border","horizontal"===r?"h-[1px] w-full":"h-full w-[1px]",e),...s}));S.displayName="Separator";let L=t.forwardRef(({className:e,...r},s)=>a.jsx("div",{ref:s,className:(0,n.cn)("animate-pulse rounded-md bg-muted",e),...r}));L.displayName="Skeleton";let Z=t.forwardRef(({className:e,children:r,...s},t)=>a.jsx("div",{ref:t,className:(0,n.cn)("min-h-screen bg-background",e),...s,children:(0,a.jsxs)("div",{className:"flex",children:[a.jsx("aside",{className:"w-64 bg-card border-r",children:a.jsx("div",{className:"p-4",children:a.jsx("h2",{className:"text-lg font-semibold",children:"FlowIQ"})})}),a.jsx("main",{className:"flex-1",children:a.jsx("div",{className:"p-6",children:r})})]})}));Z.displayName="DashboardLayout";let U=t.forwardRef(({className:e,children:r,...s},t)=>a.jsx("div",{ref:t,className:(0,n.cn)("min-h-screen flex items-center justify-center bg-background",e),...s,children:(0,a.jsxs)("div",{className:"w-full max-w-md space-y-6",children:[a.jsx("div",{className:"text-center",children:a.jsx("h1",{className:"text-2xl font-bold",children:"FlowIQ"})}),r]})}));U.displayName="AuthLayout";let M=t.forwardRef(({className:e,children:r,...s},t)=>(0,a.jsxs)("div",{ref:t,className:(0,n.cn)("min-h-screen bg-background",e),...s,children:[a.jsx("header",{className:"border-b",children:a.jsx("div",{className:"container mx-auto px-4 py-4",children:(0,a.jsxs)("nav",{className:"flex items-center justify-between",children:[a.jsx("h1",{className:"text-xl font-bold",children:"FlowIQ"}),(0,a.jsxs)("div",{className:"space-x-4",children:[a.jsx("a",{href:"#",className:"text-sm hover:underline",children:"Features"}),a.jsx("a",{href:"#",className:"text-sm hover:underline",children:"Pricing"}),a.jsx("a",{href:"#",className:"text-sm hover:underline",children:"Contact"})]})]})})}),a.jsx("main",{children:r}),a.jsx("footer",{className:"border-t mt-auto",children:a.jsx("div",{className:"container mx-auto px-4 py-6",children:a.jsx("p",{className:"text-center text-sm text-muted-foreground",children:"\xa9 2024 FlowIQ. All rights reserved."})})})]}));M.displayName="PublicLayout";let Q=t.forwardRef(({className:e,label:r,error:s,required:t,children:l,...i},d)=>(0,a.jsxs)("div",{ref:d,className:(0,n.cn)("space-y-2",e),...i,children:[r&&(0,a.jsxs)("label",{className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:[r,t&&a.jsx("span",{className:"text-red-500 ml-1",children:"*"})]}),l,s&&a.jsx("p",{className:"text-sm text-red-500",children:s})]}));Q.displayName="FormField";let $=t.forwardRef(({className:e,children:r,title:s,description:t,...l},i)=>(0,a.jsxs)("form",{ref:i,className:(0,n.cn)("space-y-6",e),...l,children:[(s||t)&&(0,a.jsxs)("div",{className:"space-y-2",children:[s&&a.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:s}),t&&a.jsx("p",{className:"text-muted-foreground",children:t})]}),r]}));$.displayName="FormWrapper"},7272:()=>{}};