"use strict";(()=>{var e={};e.id=641,e.ids=[641],e.modules={517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3581:(e,i,s)=>{s.r(i),s.d(i,{headerHooks:()=>u,originalPathname:()=>m,requestAsyncStorage:()=>d,routeModule:()=>r,serverHooks:()=>l,staticGenerationAsyncStorage:()=>c,staticGenerationBailout:()=>p});var n={};s.r(n),s.d(n,{GET:()=>GET});var t=s(884),a=s(6132),o=s(5798);async function GET(e){let{searchParams:i}=new URL(e.url),s=i.get("businessId");if(!s)return o.Z.json({error:"Business ID required"},{status:400});try{let e=await getBusinessProfile(s);if(!e)return o.Z.json({error:"Business not found"},{status:404});let i=generateAdaptiveDashboardConfig(e);return o.Z.json(i)}catch(e){return console.error("Failed to load dashboard config:",e),o.Z.json({error:"Internal server error"},{status:500})}}async function getBusinessProfile(e){return{id:e,organizationId:"Demo Business",businessType:"product-based",industry:"retail",selectedProducts:["flow-scan","cash-flow-management","product-management"],businessSize:"small",employeeCount:5,location:{province:"Gauteng",city:"Johannesburg",isRural:!1},compliance:{vatRegistered:!0,vatNumber:"4123456789",cipcRegistered:!0,registrationNumber:"2020/123456/07"},preferences:{primaryLanguage:"en",currency:"ZAR",fiscalYearStart:"2024-03-01",workingHours:{start:"08:00",end:"17:00",workingDays:["monday","tuesday","wednesday","thursday","friday"]}},onboardingCompleted:!0,createdAt:new Date,updatedAt:new Date}}function generateAdaptiveDashboardConfig(e){let i=[{id:"welcome-message",type:"welcome-message",title:"Welcome",size:"full-width",position:{row:0,col:0},visible:!0,config:{},aiEnhanced:!1}];return("product-based"===e.businessType||"hybrid"===e.businessType)&&i.push({id:"inventory-snapshot",type:"inventory-snapshot",title:"Inventory Overview",size:"large",position:{row:1,col:0},visible:!0,config:{},aiEnhanced:e.selectedProducts.includes("flow-scan")},{id:"sales-performance",type:"sales-performance",title:"Sales Performance",size:"medium",position:{row:1,col:6},visible:!0,config:{},aiEnhanced:!1}),("service-based"===e.businessType||"hybrid"===e.businessType)&&i.push({id:"active-projects",type:"active-projects",title:"Active Projects",size:"large",position:{row:1,col:0},visible:!0,config:{},aiEnhanced:e.selectedProducts.includes("business-projects")}),i.push({id:"cash-flow-summary",type:"cash-flow-summary",title:"Cash Flow",size:"large",position:{row:2,col:0},visible:!0,config:{},aiEnhanced:e.selectedProducts.includes("cash-flow-management")}),i.push({id:"ai-recommendations",type:"ai-recommendations",title:"AI Recommendations",size:"medium",position:{row:3,col:0},visible:!0,config:{},aiEnhanced:!0},{id:"industry-insights",type:"industry-insights",title:"Industry Insights",size:"medium",position:{row:3,col:4},visible:!0,config:{},aiEnhanced:!1},{id:"compliance-alerts",type:"compliance-alerts",title:"Compliance",size:"medium",position:{row:3,col:8},visible:e.compliance.vatRegistered,config:{},aiEnhanced:!1}),{businessId:e.id,layout:{primaryFocus:"product-based"===e.businessType?"inventory":"service-based"===e.businessType?"projects":"mixed",widgetOrder:i.map(e=>e.id),columnCount:12,compactMode:!1},widgets:i,preferences:{showWelcomeMessage:!0,aiRecommendationsEnabled:!0,industryInsightsEnabled:!0,complianceAlertsEnabled:e.compliance.vatRegistered,activityFeedLimit:10,refreshInterval:3e5}}}let r=new t.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/dashboard/config/route",pathname:"/api/dashboard/config",filename:"route",bundlePath:"app/api/dashboard/config/route"},resolvedPagePath:"/Users/<USER>/Pando-Mhysa-Holdings/PROJECTS/FIQ/applications/dashboard/app/api/dashboard/config/route.ts",nextConfigOutput:"",userland:n}),{requestAsyncStorage:d,staticGenerationAsyncStorage:c,serverHooks:l,headerHooks:u,staticGenerationBailout:p}=r,m="/api/dashboard/config/route"}};var i=require("../../../../webpack-runtime.js");i.C(e);var __webpack_exec__=e=>i(i.s=e),s=i.X(0,[997],()=>__webpack_exec__(3581));module.exports=s})();