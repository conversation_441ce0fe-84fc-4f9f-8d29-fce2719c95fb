(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[179],{4878:function(n,o){"use strict";function getDeploymentIdQueryOrEmptyString(){return""}Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"getDeploymentIdQueryOrEmptyString",{enumerable:!0,get:function(){return getDeploymentIdQueryOrEmptyString}})},37:function(){"trimStart"in String.prototype||(String.prototype.trimStart=String.prototype.trimLeft),"trimEnd"in String.prototype||(String.prototype.trimEnd=String.prototype.trimRight),"description"in Symbol.prototype||Object.defineProperty(Symbol.prototype,"description",{configurable:!0,get:function(){var n=/\((.*)\)/.exec(this.toString());return n?n[1]:void 0}}),Array.prototype.flat||(Array.prototype.flat=function(n,o){return o=this.concat.apply([],this),n>1&&o.some(Array.isArray)?o.flat(n-1):o},Array.prototype.flatMap=function(n,o){return this.map(n,o).flat()}),Promise.prototype.finally||(Promise.prototype.finally=function(n){if("function"!=typeof n)return this.then(n,n);var o=this.constructor||Promise;return this.then(function(u){return o.resolve(n()).then(function(){return u})},function(u){return o.resolve(n()).then(function(){throw u})})}),Object.fromEntries||(Object.fromEntries=function(n){return Array.from(n).reduce(function(n,o){return n[o[0]]=o[1],n},{})}),Array.prototype.at||(Array.prototype.at=function(n){var o=Math.trunc(n)||0;if(o<0&&(o+=this.length),!(o<0||o>=this.length))return this[o]})},5864:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"addBasePath",{enumerable:!0,get:function(){return addBasePath}});let s=u(3776),f=u(2955);function addBasePath(n,o){return(0,f.normalizePathTrailingSlash)((0,s.addPathPrefix)(n,""))}("function"==typeof o.default||"object"==typeof o.default&&null!==o.default)&&void 0===o.default.__esModule&&(Object.defineProperty(o.default,"__esModule",{value:!0}),Object.assign(o.default,o),n.exports=o.default)},2048:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"addLocale",{enumerable:!0,get:function(){return addLocale}}),u(2955);let addLocale=function(n){for(var o=arguments.length,u=Array(o>1?o-1:0),s=1;s<o;s++)u[s-1]=arguments[s];return n};("function"==typeof o.default||"object"==typeof o.default&&null!==o.default)&&void 0===o.default.__esModule&&(Object.defineProperty(o.default,"__esModule",{value:!0}),Object.assign(o.default,o),n.exports=o.default)},4287:function(n,o){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"detectDomainLocale",{enumerable:!0,get:function(){return detectDomainLocale}});let detectDomainLocale=function(){for(var n=arguments.length,o=Array(n),u=0;u<n;u++)o[u]=arguments[u]};("function"==typeof o.default||"object"==typeof o.default&&null!==o.default)&&void 0===o.default.__esModule&&(Object.defineProperty(o.default,"__esModule",{value:!0}),Object.assign(o.default,o),n.exports=o.default)},6948:function(n,o){"use strict";let u;Object.defineProperty(o,"__esModule",{value:!0}),function(n,o){for(var u in o)Object.defineProperty(n,u,{enumerable:!0,get:o[u]})}(o,{addMessageListener:function(){return addMessageListener},sendMessage:function(){return sendMessage},connectHMR:function(){return connectHMR}});let s=[];function getSocketProtocol(n){let o=location.protocol;try{o=new URL(n).protocol}catch(n){}return"http:"===o?"ws":"wss"}function addMessageListener(n){s.push(n)}function sendMessage(n){if(u&&u.readyState===u.OPEN)return u.send(n)}let f=0;function connectHMR(n){function init(){let o;function handleOnline(){f=0,window.console.log("[HMR] connected")}function handleMessage(n){let o=JSON.parse(n.data);for(let n of s)n(o)}function handleDisconnect(){if(u.onerror=null,u.onclose=null,u.close(),++f>25){window.location.reload();return}clearTimeout(o),o=setTimeout(init,f>5?5e3:1e3)}u&&u.close();let{hostname:g,port:_}=location,y=getSocketProtocol(n.assetPrefix||""),P=n.assetPrefix.replace(/^\/+/,""),b=y+"://"+g+":"+_+(P?"/"+P:"");P.startsWith("http")&&(b=y+"://"+P.split("://",2)[1]),(u=new window.WebSocket(""+b+n.path)).onopen=handleOnline,u.onerror=handleDisconnect,u.onclose=handleDisconnect,u.onmessage=handleMessage}init()}("function"==typeof o.default||"object"==typeof o.default&&null!==o.default)&&void 0===o.default.__esModule&&(Object.defineProperty(o.default,"__esModule",{value:!0}),Object.assign(o.default,o),n.exports=o.default)},5213:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"hasBasePath",{enumerable:!0,get:function(){return hasBasePath}});let s=u(6315);function hasBasePath(n){return(0,s.pathHasPrefix)(n,"")}("function"==typeof o.default||"object"==typeof o.default&&null!==o.default)&&void 0===o.default.__esModule&&(Object.defineProperty(o.default,"__esModule",{value:!0}),Object.assign(o.default,o),n.exports=o.default)},4477:function(n,o){"use strict";let u;Object.defineProperty(o,"__esModule",{value:!0}),function(n,o){for(var u in o)Object.defineProperty(n,u,{enumerable:!0,get:o[u]})}(o,{DOMAttributeNames:function(){return s},isEqualNode:function(){return isEqualNode},default:function(){return initHeadManager}});let s={acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv",noModule:"noModule"};function reactElementToDOM(n){let{type:o,props:u}=n,f=document.createElement(o);for(let n in u){if(!u.hasOwnProperty(n)||"children"===n||"dangerouslySetInnerHTML"===n||void 0===u[n])continue;let g=s[n]||n.toLowerCase();"script"===o&&("async"===g||"defer"===g||"noModule"===g)?f[g]=!!u[n]:f.setAttribute(g,u[n])}let{children:g,dangerouslySetInnerHTML:_}=u;return _?f.innerHTML=_.__html||"":g&&(f.textContent="string"==typeof g?g:Array.isArray(g)?g.join(""):""),f}function isEqualNode(n,o){if(n instanceof HTMLElement&&o instanceof HTMLElement){let u=o.getAttribute("nonce");if(u&&!n.getAttribute("nonce")){let s=o.cloneNode(!0);return s.setAttribute("nonce",""),s.nonce=u,u===n.nonce&&n.isEqualNode(s)}}return n.isEqualNode(o)}function initHeadManager(){return{mountedInstances:new Set,updateHead:n=>{let o={};n.forEach(n=>{if("link"===n.type&&n.props["data-optimized-fonts"]){if(document.querySelector('style[data-href="'+n.props["data-href"]+'"]'))return;n.props.href=n.props["data-href"],n.props["data-href"]=void 0}let u=o[n.type]||[];u.push(n),o[n.type]=u});let s=o.title?o.title[0]:null,f="";if(s){let{children:n}=s.props;f="string"==typeof n?n:Array.isArray(n)?n.join(""):""}f!==document.title&&(document.title=f),["meta","base","link","style","script"].forEach(n=>{u(n,o[n]||[])})}}}u=(n,o)=>{let u=document.getElementsByTagName("head")[0],s=u.querySelector("meta[name=next-head-count]"),f=Number(s.content),g=[];for(let o=0,u=s.previousElementSibling;o<f;o++,u=(null==u?void 0:u.previousElementSibling)||null){var _;(null==u?void 0:null==(_=u.tagName)?void 0:_.toLowerCase())===n&&g.push(u)}let y=o.map(reactElementToDOM).filter(n=>{for(let o=0,u=g.length;o<u;o++){let u=g[o];if(isEqualNode(u,n))return g.splice(o,1),!1}return!0});g.forEach(n=>{var o;return null==(o=n.parentNode)?void 0:o.removeChild(n)}),y.forEach(n=>u.insertBefore(n,s)),s.content=(f-g.length+y.length).toString()},("function"==typeof o.default||"object"==typeof o.default&&null!==o.default)&&void 0===o.default.__esModule&&(Object.defineProperty(o.default,"__esModule",{value:!0}),Object.assign(o.default,o),n.exports=o.default)},223:function(n,o,u){"use strict";let s,f,g,_,y,P,b,E,S,R,w,O;Object.defineProperty(o,"__esModule",{value:!0});let j=u(6794);Object.defineProperty(o,"__esModule",{value:!0}),function(n,o){for(var u in o)Object.defineProperty(n,u,{enumerable:!0,get:o[u]})}(o,{version:function(){return er},router:function(){return s},emitter:function(){return en},initialize:function(){return initialize},hydrate:function(){return hydrate}});let A=u(3219);u(37);let C=A._(u(2784)),M=A._(u(7029)),I=u(7768),x=A._(u(3745)),L=u(637),N=u(9987),D=u(8011),k=u(4828),U=u(510),F=u(1306),H=u(5091),B=A._(u(4477)),W=A._(u(7375)),q=A._(u(3129)),z=u(9513),G=u(4751),V=u(676),X=u(4955),K=u(7786),Y=u(5213),Q=u(1158),$=u(7178),J=u(8197),Z=A._(u(2565)),ee=A._(u(2295)),et=A._(u(6077)),er="14.0.0",en=(0,x.default)(),looseToArray=n=>[].slice.call(n),ea=!1;let Container=class Container extends C.default.Component{componentDidCatch(n,o){this.props.fn(n,o)}componentDidMount(){this.scrollToHash(),s.isSsr&&(f.isFallback||f.nextExport&&((0,D.isDynamicRoute)(s.pathname)||location.search||ea)||f.props&&f.props.__N_SSG&&(location.search||ea))&&s.replace(s.pathname+"?"+String((0,k.assign)((0,k.urlQueryToSearchParams)(s.query),new URLSearchParams(location.search))),g,{_h:1,shallow:!f.isFallback&&!ea}).catch(n=>{if(!n.cancelled)throw n})}componentDidUpdate(){this.scrollToHash()}scrollToHash(){let{hash:n}=location;if(!(n=n&&n.substring(1)))return;let o=document.getElementById(n);o&&setTimeout(()=>o.scrollIntoView(),0)}render(){return this.props.children}};async function initialize(n){void 0===n&&(n={}),ee.default.onSpanEnd(et.default),f=JSON.parse(document.getElementById("__NEXT_DATA__").textContent),window.__NEXT_DATA__=f,O=f.defaultLocale;let o=f.assetPrefix||"";if(self.__next_set_public_path__(""+o+"/_next/"),(0,U.setConfig)({serverRuntimeConfig:{},publicRuntimeConfig:f.runtimeConfig||{}}),g=(0,F.getURL)(),(0,Y.hasBasePath)(g)&&(g=(0,K.removeBasePath)(g)),f.scriptLoader){let{initScriptLoader:n}=u(2288);n(f.scriptLoader)}_=new W.default(f.buildId,o);let register=n=>{let[o,u]=n;return _.routeLoader.onEntrypoint(o,u)};return window.__NEXT_P&&window.__NEXT_P.map(n=>setTimeout(()=>register(n),0)),window.__NEXT_P=[],window.__NEXT_P.push=register,(P=(0,B.default)()).getIsSsr=()=>s.isSsr,y=document.getElementById("__next"),{assetPrefix:o}}function renderApp(n,o){return C.default.createElement(n,o)}function AppContainer(n){var o;let{children:u}=n,f=C.default.useMemo(()=>(0,$.adaptForAppRouterInstance)(s),[]);return C.default.createElement(Container,{fn:n=>renderError({App:S,err:n}).catch(n=>console.error("Error rendering page: ",n))},C.default.createElement(Q.AppRouterContext.Provider,{value:f},C.default.createElement(J.SearchParamsContext.Provider,{value:(0,$.adaptForSearchParams)(s)},C.default.createElement($.PathnameContextProviderAdapter,{router:s,isAutoExport:null!=(o=self.__NEXT_DATA__.autoExport)&&o},C.default.createElement(J.PathParamsContext.Provider,{value:(0,$.adaptForPathParams)(s)},C.default.createElement(L.RouterContext.Provider,{value:(0,G.makePublicRouterInstance)(s)},C.default.createElement(I.HeadManagerContext.Provider,{value:P},C.default.createElement(X.ImageConfigContext.Provider,{value:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}},u))))))))}let wrapApp=n=>o=>{let u={...o,Component:w,err:f.err,router:s};return C.default.createElement(AppContainer,null,renderApp(n,u))};function renderError(n){let{App:o,err:y}=n;return console.error(y),console.error("A client-side exception has occurred, see here for more info: https://nextjs.org/docs/messages/client-side-exception-occurred"),_.loadPage("/_error").then(s=>{let{page:f,styleSheets:g}=s;return(null==b?void 0:b.Component)===f?Promise.resolve().then(()=>j._(u(4570))).then(s=>Promise.resolve().then(()=>j._(u(8955))).then(u=>(o=u.default,n.App=o,s))).then(n=>({ErrorComponent:n.default,styleSheets:[]})):{ErrorComponent:f,styleSheets:g}}).then(u=>{var _;let{ErrorComponent:P,styleSheets:b}=u,E=wrapApp(o),S={Component:P,AppTree:E,router:s,ctx:{err:y,pathname:f.page,query:f.query,asPath:g,AppTree:E}};return Promise.resolve((null==(_=n.props)?void 0:_.err)?n.props:(0,F.loadGetInitialProps)(o,S)).then(o=>doRender({...n,err:y,Component:P,styleSheets:b,props:o}))})}function Head(n){let{callback:o}=n;return C.default.useLayoutEffect(()=>o(),[o]),null}let eo={navigationStart:"navigationStart",beforeRender:"beforeRender",afterRender:"afterRender",afterHydrate:"afterHydrate",routeChange:"routeChange"},ei={hydration:"Next.js-hydration",beforeHydration:"Next.js-before-hydration",routeChangeToRender:"Next.js-route-change-to-render",render:"Next.js-render"},el=null,eu=!0;function clearMarks(){[eo.beforeRender,eo.afterHydrate,eo.afterRender,eo.routeChange].forEach(n=>performance.clearMarks(n))}function markHydrateComplete(){if(!F.ST)return;performance.mark(eo.afterHydrate);let n=performance.getEntriesByName(eo.beforeRender,"mark").length;n&&(performance.measure(ei.beforeHydration,eo.navigationStart,eo.beforeRender),performance.measure(ei.hydration,eo.beforeRender,eo.afterHydrate)),R&&performance.getEntriesByName(ei.hydration).forEach(R),clearMarks()}function markRenderComplete(){if(!F.ST)return;performance.mark(eo.afterRender);let n=performance.getEntriesByName(eo.routeChange,"mark");if(!n.length)return;let o=performance.getEntriesByName(eo.beforeRender,"mark").length;o&&(performance.measure(ei.routeChangeToRender,n[0].name,eo.beforeRender),performance.measure(ei.render,eo.beforeRender,eo.afterRender),R&&(performance.getEntriesByName(ei.render).forEach(R),performance.getEntriesByName(ei.routeChangeToRender).forEach(R))),clearMarks(),[ei.routeChangeToRender,ei.render].forEach(n=>performance.clearMeasures(n))}function renderReactElement(n,o){F.ST&&performance.mark(eo.beforeRender);let u=o(eu?markHydrateComplete:markRenderComplete);if(el){let n=C.default.startTransition;n(()=>{el.render(u)})}else el=M.default.hydrateRoot(n,u,{onRecoverableError:Z.default}),eu=!1}function Root(n){let{callbacks:o,children:u}=n;return C.default.useLayoutEffect(()=>o.forEach(n=>n()),[o]),C.default.useEffect(()=>{(0,q.default)(R)},[]),u}function doRender(n){let o,{App:u,Component:f,props:g,err:_}=n,P="initial"in n?void 0:n.styleSheets;f=f||b.Component,g=g||b.props;let S={...g,Component:f,err:_,router:s};b=S;let R=!1,w=new Promise((n,u)=>{E&&E(),o=()=>{E=null,n()},E=()=>{R=!0,E=null;let n=Error("Cancel rendering route");n.cancelled=!0,u(n)}});function onHeadCommit(){if(P&&!R){let n=new Set(P.map(n=>n.href)),o=looseToArray(document.querySelectorAll("style[data-n-href]")),u=o.map(n=>n.getAttribute("data-n-href"));for(let s=0;s<u.length;++s)n.has(u[s])?o[s].removeAttribute("media"):o[s].setAttribute("media","x");let s=document.querySelector("noscript[data-n-css]");s&&P.forEach(n=>{let{href:o}=n,u=document.querySelector('style[data-n-href="'+o+'"]');u&&(s.parentNode.insertBefore(u,s.nextSibling),s=u)}),looseToArray(document.querySelectorAll("link[data-n-p]")).forEach(n=>{n.parentNode.removeChild(n)})}if(n.scroll){let{x:o,y:u}=n.scroll;(0,N.handleSmoothScroll)(()=>{window.scrollTo(o,u)})}}function onRootCommit(){o()}!function(){if(!P)return;let n=looseToArray(document.querySelectorAll("style[data-n-href]")),o=new Set(n.map(n=>n.getAttribute("data-n-href"))),u=document.querySelector("noscript[data-n-css]"),s=null==u?void 0:u.getAttribute("data-n-css");P.forEach(n=>{let{href:u,text:f}=n;if(!o.has(u)){let n=document.createElement("style");n.setAttribute("data-n-href",u),n.setAttribute("media","x"),s&&n.setAttribute("nonce",s),document.head.appendChild(n),n.appendChild(document.createTextNode(f))}})}();let O=C.default.createElement(C.default.Fragment,null,C.default.createElement(Head,{callback:onHeadCommit}),C.default.createElement(AppContainer,null,renderApp(u,S),C.default.createElement(H.Portal,{type:"next-route-announcer"},C.default.createElement(z.RouteAnnouncer,null))));return renderReactElement(y,n=>C.default.createElement(Root,{callbacks:[n,onRootCommit]},O)),w}async function render(n){if(n.err){await renderError(n);return}try{await doRender(n)}catch(u){let o=(0,V.getProperError)(u);if(o.cancelled)throw o;await renderError({...n,err:o})}}async function hydrate(n){let o=f.err;try{let n=await _.routeLoader.whenEntrypoint("/_app");if("error"in n)throw n.error;let{component:o,exports:u}=n;S=o,u&&u.reportWebVitals&&(R=n=>{let o,{id:s,name:f,startTime:g,value:_,duration:y,entryType:P,entries:b,attribution:E}=n,S=Date.now()+"-"+(Math.floor(Math.random()*(9e12-1))+1e12);b&&b.length&&(o=b[0].startTime);let R={id:s||S,name:f,startTime:g||o,value:null==_?y:_,label:"mark"===P||"measure"===P?"custom":"web-vital"};E&&(R.attribution=E),u.reportWebVitals(R)});let s=await _.routeLoader.whenEntrypoint(f.page);if("error"in s)throw s.error;w=s.component}catch(n){o=(0,V.getProperError)(n)}window.__NEXT_PRELOADREADY&&await window.__NEXT_PRELOADREADY(f.dynamicIds),s=(0,G.createRouter)(f.page,f.query,g,{initialProps:f.props,pageLoader:_,App:S,Component:w,wrapApp,err:o,isFallback:!!f.isFallback,subscription:(n,o,u)=>render(Object.assign({},n,{App:o,scroll:u})),locale:f.locale,locales:f.locales,defaultLocale:O,domainLocales:f.domainLocales,isPreview:f.isPreview}),ea=await s._initialMatchesMiddlewarePromise;let u={App:S,initial:!0,Component:w,props:f.props,err:o};(null==n?void 0:n.beforeRender)&&await n.beforeRender(),render(u)}("function"==typeof o.default||"object"==typeof o.default&&null!==o.default)&&void 0===o.default.__esModule&&(Object.defineProperty(o.default,"__esModule",{value:!0}),Object.assign(o.default,o),n.exports=o.default)},6653:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),u(1867);let s=u(223);window.next={version:s.version,get router(){return s.router},emitter:s.emitter},(0,s.initialize)({}).then(()=>(0,s.hydrate)()).catch(console.error),("function"==typeof o.default||"object"==typeof o.default&&null!==o.default)&&void 0===o.default.__esModule&&(Object.defineProperty(o.default,"__esModule",{value:!0}),Object.assign(o.default,o),n.exports=o.default)},2955:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return normalizePathTrailingSlash}});let s=u(6082),f=u(891),normalizePathTrailingSlash=n=>{if(!n.startsWith("/"))return n;let{pathname:o,query:u,hash:g}=(0,f.parsePath)(n);return""+(0,s.removeTrailingSlash)(o)+u+g};("function"==typeof o.default||"object"==typeof o.default&&null!==o.default)&&void 0===o.default.__esModule&&(Object.defineProperty(o.default,"__esModule",{value:!0}),Object.assign(o.default,o),n.exports=o.default)},2565:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"default",{enumerable:!0,get:function(){return onRecoverableError}});let s=u(4600);function onRecoverableError(n){let o="function"==typeof reportError?reportError:n=>{window.console.error(n)};n.digest!==s.NEXT_DYNAMIC_NO_SSR_CODE&&o(n)}("function"==typeof o.default||"object"==typeof o.default&&null!==o.default)&&void 0===o.default.__esModule&&(Object.defineProperty(o.default,"__esModule",{value:!0}),Object.assign(o.default,o),n.exports=o.default)},7375:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"default",{enumerable:!0,get:function(){return PageLoader}});let s=u(3219),f=u(5864),g=u(9381),_=s._(u(8052)),y=u(2048),P=u(8011),b=u(9823),E=u(6082),S=u(7159);u(729);let PageLoader=class PageLoader{getPageList(){return(0,S.getClientBuildManifest)().then(n=>n.sortedPages)}getMiddleware(){return window.__MIDDLEWARE_MATCHERS=[],window.__MIDDLEWARE_MATCHERS}getDataHref(n){let{asPath:o,href:u,locale:s}=n,{pathname:S,query:R,search:w}=(0,b.parseRelativeUrl)(u),{pathname:O}=(0,b.parseRelativeUrl)(o),j=(0,E.removeTrailingSlash)(S);if("/"!==j[0])throw Error('Route name should start with a "/", got "'+j+'"');return(n=>{let o=(0,_.default)((0,E.removeTrailingSlash)((0,y.addLocale)(n,s)),".json");return(0,f.addBasePath)("/_next/data/"+this.buildId+o+w,!0)})(n.skipInterpolation?O:(0,P.isDynamicRoute)(j)?(0,g.interpolateAs)(S,O,R).result:j)}_isSsg(n){return this.promisedSsgManifest.then(o=>o.has(n))}loadPage(n){return this.routeLoader.loadRoute(n).then(n=>{if("component"in n)return{page:n.component,mod:n.exports,styleSheets:n.styles.map(n=>({href:n.href,text:n.content}))};throw n.error})}prefetch(n){return this.routeLoader.prefetch(n)}constructor(n,o){this.routeLoader=(0,S.createRouteLoader)(o),this.buildId=n,this.assetPrefix=o,this.promisedSsgManifest=new Promise(n=>{window.__SSG_MANIFEST?n(window.__SSG_MANIFEST):window.__SSG_MANIFEST_CB=()=>{n(window.__SSG_MANIFEST)}})}};("function"==typeof o.default||"object"==typeof o.default&&null!==o.default)&&void 0===o.default.__esModule&&(Object.defineProperty(o.default,"__esModule",{value:!0}),Object.assign(o.default,o),n.exports=o.default)},3129:function(n,o,u){"use strict";let s;Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"default",{enumerable:!0,get:function(){return _default}});let f=["CLS","FCP","FID","INP","LCP","TTFB"];location.href;let g=!1;function onReport(n){s&&s(n)}let _default=n=>{if(s=n,!g)for(let n of(g=!0,f))try{let o;o||(o=u(8018)),o["on"+n](onReport)}catch(o){console.warn("Failed to track "+n+" web-vital",o)}};("function"==typeof o.default||"object"==typeof o.default&&null!==o.default)&&void 0===o.default.__esModule&&(Object.defineProperty(o.default,"__esModule",{value:!0}),Object.assign(o.default,o),n.exports=o.default)},5091:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"Portal",{enumerable:!0,get:function(){return Portal}});let s=u(2784),f=u(8316),Portal=n=>{let{children:o,type:u}=n,[g,_]=(0,s.useState)(null);return(0,s.useEffect)(()=>{let n=document.createElement(u);return document.body.appendChild(n),_(n),()=>{document.body.removeChild(n)}},[u]),g?(0,f.createPortal)(o,g):null};("function"==typeof o.default||"object"==typeof o.default&&null!==o.default)&&void 0===o.default.__esModule&&(Object.defineProperty(o.default,"__esModule",{value:!0}),Object.assign(o.default,o),n.exports=o.default)},7786:function(n,o,u){"use strict";function removeBasePath(n){return n}Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"removeBasePath",{enumerable:!0,get:function(){return removeBasePath}}),u(5213),("function"==typeof o.default||"object"==typeof o.default&&null!==o.default)&&void 0===o.default.__esModule&&(Object.defineProperty(o.default,"__esModule",{value:!0}),Object.assign(o.default,o),n.exports=o.default)},3099:function(n,o,u){"use strict";function removeLocale(n,o){return n}Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"removeLocale",{enumerable:!0,get:function(){return removeLocale}}),u(891),("function"==typeof o.default||"object"==typeof o.default&&null!==o.default)&&void 0===o.default.__esModule&&(Object.defineProperty(o.default,"__esModule",{value:!0}),Object.assign(o.default,o),n.exports=o.default)},3767:function(n,o){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),function(n,o){for(var u in o)Object.defineProperty(n,u,{enumerable:!0,get:o[u]})}(o,{requestIdleCallback:function(){return u},cancelIdleCallback:function(){return s}});let u="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(n){let o=Date.now();return self.setTimeout(function(){n({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-o))}})},1)},s="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(n){return clearTimeout(n)};("function"==typeof o.default||"object"==typeof o.default&&null!==o.default)&&void 0===o.default.__esModule&&(Object.defineProperty(o.default,"__esModule",{value:!0}),Object.assign(o.default,o),n.exports=o.default)},4785:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"resolveHref",{enumerable:!0,get:function(){return resolveHref}});let s=u(4828),f=u(3833),g=u(9302),_=u(1306),y=u(2955),P=u(4131),b=u(1232),E=u(9381);function resolveHref(n,o,u){let S;let R="string"==typeof o?o:(0,f.formatWithValidation)(o),w=R.match(/^[a-zA-Z]{1,}:\/\//),O=w?R.slice(w[0].length):R,j=O.split("?",1);if((j[0]||"").match(/(\/\/|\\)/)){console.error("Invalid href '"+R+"' passed to next/router in page: '"+n.pathname+"'. Repeated forward-slashes (//) or backslashes \\ are not valid in the href.");let o=(0,_.normalizeRepeatedSlashes)(O);R=(w?w[0]:"")+o}if(!(0,P.isLocalURL)(R))return u?[R]:R;try{S=new URL(R.startsWith("#")?n.asPath:n.pathname,"http://n")}catch(n){S=new URL("/","http://n")}try{let n=new URL(R,S);n.pathname=(0,y.normalizePathTrailingSlash)(n.pathname);let o="";if((0,b.isDynamicRoute)(n.pathname)&&n.searchParams&&u){let u=(0,s.searchParamsToUrlQuery)(n.searchParams),{result:_,params:y}=(0,E.interpolateAs)(n.pathname,n.pathname,u);_&&(o=(0,f.formatWithValidation)({pathname:_,hash:n.hash,query:(0,g.omit)(u,y)}))}let _=n.origin===S.origin?n.href.slice(n.origin.length):n.href;return u?[_,o||_]:_}catch(n){return u?[R]:R}}("function"==typeof o.default||"object"==typeof o.default&&null!==o.default)&&void 0===o.default.__esModule&&(Object.defineProperty(o.default,"__esModule",{value:!0}),Object.assign(o.default,o),n.exports=o.default)},9513:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),function(n,o){for(var u in o)Object.defineProperty(n,u,{enumerable:!0,get:o[u]})}(o,{RouteAnnouncer:function(){return RouteAnnouncer},default:function(){return y}});let s=u(3219),f=s._(u(2784)),g=u(4751),_={border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"absolute",top:0,width:"1px",whiteSpace:"nowrap",wordWrap:"normal"},RouteAnnouncer=()=>{let{asPath:n}=(0,g.useRouter)(),[o,u]=f.default.useState(""),s=f.default.useRef(n);return f.default.useEffect(()=>{if(s.current!==n){if(s.current=n,document.title)u(document.title);else{var o;let s=document.querySelector("h1"),f=null!=(o=null==s?void 0:s.innerText)?o:null==s?void 0:s.textContent;u(f||n)}}},[n]),f.default.createElement("p",{"aria-live":"assertive",id:"__next-route-announcer__",role:"alert",style:_},o)},y=RouteAnnouncer;("function"==typeof o.default||"object"==typeof o.default&&null!==o.default)&&void 0===o.default.__esModule&&(Object.defineProperty(o.default,"__esModule",{value:!0}),Object.assign(o.default,o),n.exports=o.default)},7159:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),function(n,o){for(var u in o)Object.defineProperty(n,u,{enumerable:!0,get:o[u]})}(o,{markAssetError:function(){return markAssetError},isAssetError:function(){return isAssetError},getClientBuildManifest:function(){return getClientBuildManifest},createRouteLoader:function(){return createRouteLoader}}),u(3219),u(8052);let s=u(4581),f=u(3767),g=u(4878);function withFuture(n,o,u){let s,f=o.get(n);if(f)return"future"in f?f.future:Promise.resolve(f);let g=new Promise(n=>{s=n});return o.set(n,f={resolve:s,future:g}),u?u().then(n=>(s(n),n)).catch(u=>{throw o.delete(n),u}):g}let _=Symbol("ASSET_LOAD_ERROR");function markAssetError(n){return Object.defineProperty(n,_,{})}function isAssetError(n){return n&&_ in n}function hasPrefetch(n){try{return n=document.createElement("link"),!!window.MSInputMethodContext&&!!document.documentMode||n.relList.supports("prefetch")}catch(n){return!1}}let y=hasPrefetch(),getAssetQueryString=()=>(0,g.getDeploymentIdQueryOrEmptyString)();function prefetchViaDom(n,o,u){return new Promise((s,f)=>{let g='\n      link[rel="prefetch"][href^="'+n+'"],\n      link[rel="preload"][href^="'+n+'"],\n      script[src^="'+n+'"]';if(document.querySelector(g))return s();u=document.createElement("link"),o&&(u.as=o),u.rel="prefetch",u.crossOrigin=void 0,u.onload=s,u.onerror=()=>f(markAssetError(Error("Failed to prefetch: "+n))),u.href=n,document.head.appendChild(u)})}function appendScript(n,o){return new Promise((u,s)=>{(o=document.createElement("script")).onload=u,o.onerror=()=>s(markAssetError(Error("Failed to load script: "+n))),o.crossOrigin=void 0,o.src=n,document.body.appendChild(o)})}function resolvePromiseWithTimeout(n,o,u){return new Promise((s,g)=>{let _=!1;n.then(n=>{_=!0,s(n)}).catch(g),(0,f.requestIdleCallback)(()=>setTimeout(()=>{_||g(u)},o))})}function getClientBuildManifest(){if(self.__BUILD_MANIFEST)return Promise.resolve(self.__BUILD_MANIFEST);let n=new Promise(n=>{let o=self.__BUILD_MANIFEST_CB;self.__BUILD_MANIFEST_CB=()=>{n(self.__BUILD_MANIFEST),o&&o()}});return resolvePromiseWithTimeout(n,3800,markAssetError(Error("Failed to load client build manifest")))}function getFilesForRoute(n,o){return getClientBuildManifest().then(u=>{if(!(o in u))throw markAssetError(Error("Failed to lookup route: "+o));let f=u[o].map(o=>n+"/_next/"+encodeURI(o));return{scripts:f.filter(n=>n.endsWith(".js")).map(n=>(0,s.__unsafeCreateTrustedScriptURL)(n)+getAssetQueryString()),css:f.filter(n=>n.endsWith(".css")).map(n=>n+getAssetQueryString())}})}function createRouteLoader(n){let o=new Map,u=new Map,s=new Map,g=new Map;function maybeExecuteScript(n){{let o=u.get(n.toString());return o||(document.querySelector('script[src^="'+n+'"]')?Promise.resolve():(u.set(n.toString(),o=appendScript(n)),o))}}function fetchStyleSheet(n){let o=s.get(n);return o||s.set(n,o=fetch(n).then(o=>{if(!o.ok)throw Error("Failed to load stylesheet: "+n);return o.text().then(o=>({href:n,content:o}))}).catch(n=>{throw markAssetError(n)})),o}return{whenEntrypoint:n=>withFuture(n,o),onEntrypoint(n,u){(u?Promise.resolve().then(()=>u()).then(n=>({component:n&&n.default||n,exports:n}),n=>({error:n})):Promise.resolve(void 0)).then(u=>{let s=o.get(n);s&&"resolve"in s?u&&(o.set(n,u),s.resolve(u)):(u?o.set(n,u):o.delete(n),g.delete(n))})},loadRoute(u,s){return withFuture(u,g,()=>{let f;return resolvePromiseWithTimeout(getFilesForRoute(n,u).then(n=>{let{scripts:s,css:f}=n;return Promise.all([o.has(u)?[]:Promise.all(s.map(maybeExecuteScript)),Promise.all(f.map(fetchStyleSheet))])}).then(n=>this.whenEntrypoint(u).then(o=>({entrypoint:o,styles:n[1]}))),3800,markAssetError(Error("Route did not complete loading: "+u))).then(n=>{let{entrypoint:o,styles:u}=n,s=Object.assign({styles:u},o);return"error"in o?o:s}).catch(n=>{if(s)throw n;return{error:n}}).finally(()=>null==f?void 0:f())})},prefetch(o){let u;return(u=navigator.connection)&&(u.saveData||/2g/.test(u.effectiveType))?Promise.resolve():getFilesForRoute(n,o).then(n=>Promise.all(y?n.scripts.map(n=>prefetchViaDom(n.toString(),"script")):[])).then(()=>{(0,f.requestIdleCallback)(()=>this.loadRoute(o,!0).catch(()=>{}))}).catch(()=>{})}}}("function"==typeof o.default||"object"==typeof o.default&&null!==o.default)&&void 0===o.default.__esModule&&(Object.defineProperty(o.default,"__esModule",{value:!0}),Object.assign(o.default,o),n.exports=o.default)},4751:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),function(n,o){for(var u in o)Object.defineProperty(n,u,{enumerable:!0,get:o[u]})}(o,{Router:function(){return g.default},default:function(){return R},withRouter:function(){return P.default},useRouter:function(){return useRouter},createRouter:function(){return createRouter},makePublicRouterInstance:function(){return makePublicRouterInstance}});let s=u(3219),f=s._(u(2784)),g=s._(u(7797)),_=u(637),y=s._(u(676)),P=s._(u(7727)),b={router:null,readyCallbacks:[],ready(n){if(this.router)return n();this.readyCallbacks.push(n)}},E=["pathname","route","query","asPath","components","isFallback","basePath","locale","locales","defaultLocale","isReady","isPreview","isLocaleDomain","domainLocales"],S=["push","replace","reload","back","prefetch","beforePopState"];function getRouter(){if(!b.router)throw Error('No router instance found.\nYou should only use "next/router" on the client side of your app.\n');return b.router}Object.defineProperty(b,"events",{get:()=>g.default.events}),E.forEach(n=>{Object.defineProperty(b,n,{get(){let o=getRouter();return o[n]}})}),S.forEach(n=>{b[n]=function(){for(var o=arguments.length,u=Array(o),s=0;s<o;s++)u[s]=arguments[s];let f=getRouter();return f[n](...u)}}),["routeChangeStart","beforeHistoryChange","routeChangeComplete","routeChangeError","hashChangeStart","hashChangeComplete"].forEach(n=>{b.ready(()=>{g.default.events.on(n,function(){for(var o=arguments.length,u=Array(o),s=0;s<o;s++)u[s]=arguments[s];let f="on"+n.charAt(0).toUpperCase()+n.substring(1);if(b[f])try{b[f](...u)}catch(n){console.error("Error when running the Router event: "+f),console.error((0,y.default)(n)?n.message+"\n"+n.stack:n+"")}})})});let R=b;function useRouter(){let n=f.default.useContext(_.RouterContext);if(!n)throw Error("NextRouter was not mounted. https://nextjs.org/docs/messages/next-router-not-mounted");return n}function createRouter(){for(var n=arguments.length,o=Array(n),u=0;u<n;u++)o[u]=arguments[u];return b.router=new g.default(...o),b.readyCallbacks.forEach(n=>n()),b.readyCallbacks=[],b.router}function makePublicRouterInstance(n){let o={};for(let u of E){if("object"==typeof n[u]){o[u]=Object.assign(Array.isArray(n[u])?[]:{},n[u]);continue}o[u]=n[u]}return o.events=g.default.events,S.forEach(u=>{o[u]=function(){for(var o=arguments.length,s=Array(o),f=0;f<o;f++)s[f]=arguments[f];return n[u](...s)}}),o}("function"==typeof o.default||"object"==typeof o.default&&null!==o.default)&&void 0===o.default.__esModule&&(Object.defineProperty(o.default,"__esModule",{value:!0}),Object.assign(o.default,o),n.exports=o.default)},2288:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),function(n,o){for(var u in o)Object.defineProperty(n,u,{enumerable:!0,get:o[u]})}(o,{handleClientScriptLoad:function(){return handleClientScriptLoad},initScriptLoader:function(){return initScriptLoader},default:function(){return w}});let s=u(3219),f=u(6794),g=s._(u(8316)),_=f._(u(2784)),y=u(7768),P=u(4477),b=u(3767),E=new Map,S=new Set,R=["onLoad","onReady","dangerouslySetInnerHTML","children","onError","strategy","stylesheets"],insertStylesheets=n=>{if(g.default.preinit){n.forEach(n=>{g.default.preinit(n,{as:"style"})});return}{let o=document.head;n.forEach(n=>{let u=document.createElement("link");u.type="text/css",u.rel="stylesheet",u.href=n,o.appendChild(u)})}},loadScript=n=>{let{src:o,id:u,onLoad:s=()=>{},onReady:f=null,dangerouslySetInnerHTML:g,children:_="",strategy:y="afterInteractive",onError:b,stylesheets:w}=n,O=u||o;if(O&&S.has(O))return;if(E.has(o)){S.add(O),E.get(o).then(s,b);return}let afterLoad=()=>{f&&f(),S.add(O)},j=document.createElement("script"),A=new Promise((n,o)=>{j.addEventListener("load",function(o){n(),s&&s.call(this,o),afterLoad()}),j.addEventListener("error",function(n){o(n)})}).catch(function(n){b&&b(n)});for(let[u,s]of(g?(j.innerHTML=g.__html||"",afterLoad()):_?(j.textContent="string"==typeof _?_:Array.isArray(_)?_.join(""):"",afterLoad()):o&&(j.src=o,E.set(o,A)),Object.entries(n))){if(void 0===s||R.includes(u))continue;let n=P.DOMAttributeNames[u]||u.toLowerCase();j.setAttribute(n,s)}"worker"===y&&j.setAttribute("type","text/partytown"),j.setAttribute("data-nscript",y),w&&insertStylesheets(w),document.body.appendChild(j)};function handleClientScriptLoad(n){let{strategy:o="afterInteractive"}=n;"lazyOnload"===o?window.addEventListener("load",()=>{(0,b.requestIdleCallback)(()=>loadScript(n))}):loadScript(n)}function loadLazyScript(n){"complete"===document.readyState?(0,b.requestIdleCallback)(()=>loadScript(n)):window.addEventListener("load",()=>{(0,b.requestIdleCallback)(()=>loadScript(n))})}function addBeforeInteractiveToCache(){let n=[...document.querySelectorAll('[data-nscript="beforeInteractive"]'),...document.querySelectorAll('[data-nscript="beforePageRender"]')];n.forEach(n=>{let o=n.id||n.getAttribute("src");S.add(o)})}function initScriptLoader(n){n.forEach(handleClientScriptLoad),addBeforeInteractiveToCache()}function Script(n){let{id:o,src:u="",onLoad:s=()=>{},onReady:f=null,strategy:P="afterInteractive",onError:b,stylesheets:E,...R}=n,{updateScripts:w,scripts:O,getIsSsr:j,appDir:A,nonce:C}=(0,_.useContext)(y.HeadManagerContext),M=(0,_.useRef)(!1);(0,_.useEffect)(()=>{let n=o||u;M.current||(f&&n&&S.has(n)&&f(),M.current=!0)},[f,o,u]);let I=(0,_.useRef)(!1);if((0,_.useEffect)(()=>{I.current||("afterInteractive"===P?loadScript(n):"lazyOnload"===P&&loadLazyScript(n),I.current=!0)},[n,P]),("beforeInteractive"===P||"worker"===P)&&(w?(O[P]=(O[P]||[]).concat([{id:o,src:u,onLoad:s,onReady:f,onError:b,...R}]),w(O)):j&&j()?S.add(o||u):j&&!j()&&loadScript(n)),A){if(E&&E.forEach(n=>{g.default.preinit(n,{as:"style"})}),"beforeInteractive"===P)return u?(g.default.preload(u,R.integrity?{as:"script",integrity:R.integrity}:{as:"script"}),_.default.createElement("script",{nonce:C,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([u])+")"}})):(R.dangerouslySetInnerHTML&&(R.children=R.dangerouslySetInnerHTML.__html,delete R.dangerouslySetInnerHTML),_.default.createElement("script",{nonce:C,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([0,{...R}])+")"}}));"afterInteractive"===P&&u&&g.default.preload(u,R.integrity?{as:"script",integrity:R.integrity}:{as:"script"})}return null}Object.defineProperty(Script,"__nextScript",{value:!0});let w=Script;("function"==typeof o.default||"object"==typeof o.default&&null!==o.default)&&void 0===o.default.__esModule&&(Object.defineProperty(o.default,"__esModule",{value:!0}),Object.assign(o.default,o),n.exports=o.default)},6077:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"default",{enumerable:!0,get:function(){return reportToSocket}});let s=u(6948);function reportToSocket(n){if("ended"!==n.state.state)throw Error("Expected span to be ended");(0,s.sendMessage)(JSON.stringify({event:"span-end",startTime:n.startTime,endTime:n.state.endTime,spanName:n.name,attributes:n.attributes}))}("function"==typeof o.default||"object"==typeof o.default&&null!==o.default)&&void 0===o.default.__esModule&&(Object.defineProperty(o.default,"__esModule",{value:!0}),Object.assign(o.default,o),n.exports=o.default)},2295:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"default",{enumerable:!0,get:function(){return g}});let s=u(3219),f=s._(u(3745));let Span=class Span{end(n){if("ended"===this.state.state)throw Error("Span has already ended");this.state={state:"ended",endTime:null!=n?n:Date.now()},this.onSpanEnd(this)}constructor(n,o,u){var s,f;this.name=n,this.attributes=null!=(s=o.attributes)?s:{},this.startTime=null!=(f=o.startTime)?f:Date.now(),this.onSpanEnd=u,this.state={state:"inprogress"}}};let Tracer=class Tracer{startSpan(n,o){return new Span(n,o,this.handleSpanEnd)}onSpanEnd(n){return this._emitter.on("spanend",n),()=>{this._emitter.off("spanend",n)}}constructor(){this._emitter=(0,f.default)(),this.handleSpanEnd=n=>{this._emitter.emit("spanend",n)}}};let g=new Tracer;("function"==typeof o.default||"object"==typeof o.default&&null!==o.default)&&void 0===o.default.__esModule&&(Object.defineProperty(o.default,"__esModule",{value:!0}),Object.assign(o.default,o),n.exports=o.default)},4581:function(n,o){"use strict";let u;function getPolicy(){if(void 0===u){var n;u=(null==(n=window.trustedTypes)?void 0:n.createPolicy("nextjs",{createHTML:n=>n,createScript:n=>n,createScriptURL:n=>n}))||null}return u}function __unsafeCreateTrustedScriptURL(n){var o;return(null==(o=getPolicy())?void 0:o.createScriptURL(n))||n}Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"__unsafeCreateTrustedScriptURL",{enumerable:!0,get:function(){return __unsafeCreateTrustedScriptURL}}),("function"==typeof o.default||"object"==typeof o.default&&null!==o.default)&&void 0===o.default.__esModule&&(Object.defineProperty(o.default,"__esModule",{value:!0}),Object.assign(o.default,o),n.exports=o.default)},1867:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),u(4878),self.__next_set_public_path__=n=>{u.p=n},("function"==typeof o.default||"object"==typeof o.default&&null!==o.default)&&void 0===o.default.__esModule&&(Object.defineProperty(o.default,"__esModule",{value:!0}),Object.assign(o.default,o),n.exports=o.default)},7727:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"default",{enumerable:!0,get:function(){return withRouter}});let s=u(3219),f=s._(u(2784)),g=u(4751);function withRouter(n){function WithRouterWrapper(o){return f.default.createElement(n,{router:(0,g.useRouter)(),...o})}return WithRouterWrapper.getInitialProps=n.getInitialProps,WithRouterWrapper.origGetInitialProps=n.origGetInitialProps,WithRouterWrapper}("function"==typeof o.default||"object"==typeof o.default&&null!==o.default)&&void 0===o.default.__esModule&&(Object.defineProperty(o.default,"__esModule",{value:!0}),Object.assign(o.default,o),n.exports=o.default)},8955:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"default",{enumerable:!0,get:function(){return App}});let s=u(3219),f=s._(u(2784)),g=u(1306);async function appGetInitialProps(n){let{Component:o,ctx:u}=n,s=await (0,g.loadGetInitialProps)(o,u);return{pageProps:s}}let App=class App extends f.default.Component{render(){let{Component:n,pageProps:o}=this.props;return f.default.createElement(n,o)}};App.origGetInitialProps=appGetInitialProps,App.getInitialProps=appGetInitialProps,("function"==typeof o.default||"object"==typeof o.default&&null!==o.default)&&void 0===o.default.__esModule&&(Object.defineProperty(o.default,"__esModule",{value:!0}),Object.assign(o.default,o),n.exports=o.default)},4570:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"default",{enumerable:!0,get:function(){return Error}});let s=u(3219),f=s._(u(2784)),g=s._(u(46)),_={400:"Bad Request",404:"This page could not be found",405:"Method Not Allowed",500:"Internal Server Error"};function _getInitialProps(n){let{res:o,err:u}=n,s=o&&o.statusCode?o.statusCode:u?u.statusCode:404;return{statusCode:s}}let y={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},desc:{lineHeight:"48px"},h1:{display:"inline-block",margin:"0 20px 0 0",paddingRight:23,fontSize:24,fontWeight:500,verticalAlign:"top"},h2:{fontSize:14,fontWeight:400,lineHeight:"28px"},wrap:{display:"inline-block"}};let Error=class Error extends f.default.Component{render(){let{statusCode:n,withDarkMode:o=!0}=this.props,u=this.props.title||_[n]||"An unexpected error has occurred";return f.default.createElement("div",{style:y.error},f.default.createElement(g.default,null,f.default.createElement("title",null,n?n+": "+u:"Application error: a client-side exception has occurred")),f.default.createElement("div",{style:y.desc},f.default.createElement("style",{dangerouslySetInnerHTML:{__html:"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}"+(o?"@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}":"")}}),n?f.default.createElement("h1",{className:"next-error-h1",style:y.h1},n):null,f.default.createElement("div",{style:y.wrap},f.default.createElement("h2",{style:y.h2},this.props.title||n?u:f.default.createElement(f.default.Fragment,null,"Application error: a client-side exception has occurred (see the browser console for more information)"),"."))))}};Error.displayName="ErrorPage",Error.getInitialProps=_getInitialProps,Error.origGetInitialProps=_getInitialProps,("function"==typeof o.default||"object"==typeof o.default&&null!==o.default)&&void 0===o.default.__esModule&&(Object.defineProperty(o.default,"__esModule",{value:!0}),Object.assign(o.default,o),n.exports=o.default)},5938:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"AmpStateContext",{enumerable:!0,get:function(){return g}});let s=u(3219),f=s._(u(2784)),g=f.default.createContext({})},8926:function(n,o){"use strict";function isInAmpMode(n){let{ampFirst:o=!1,hybrid:u=!1,hasQuery:s=!1}=void 0===n?{}:n;return o||u&&s}Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"isInAmpMode",{enumerable:!0,get:function(){return isInAmpMode}})},1158:function(n,o,u){"use strict";var s,f;Object.defineProperty(o,"__esModule",{value:!0}),function(n,o){for(var u in o)Object.defineProperty(n,u,{enumerable:!0,get:o[u]})}(o,{CacheStates:function(){return s},AppRouterContext:function(){return y},LayoutRouterContext:function(){return P},GlobalLayoutRouterContext:function(){return b},TemplateContext:function(){return E}});let g=u(3219),_=g._(u(2784));(f=s||(s={})).LAZY_INITIALIZED="LAZYINITIALIZED",f.DATA_FETCH="DATAFETCH",f.READY="READY";let y=_.default.createContext(null),P=_.default.createContext(null),b=_.default.createContext(null),E=_.default.createContext(null)},1403:function(n,o){"use strict";function murmurhash2(n){let o=0;for(let u=0;u<n.length;u++){let s=n.charCodeAt(u);o=Math.imul(o^s,1540483477),o^=o>>>13,o=Math.imul(o,1540483477)}return o>>>0}Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"BloomFilter",{enumerable:!0,get:function(){return BloomFilter}});let BloomFilter=class BloomFilter{static from(n,o){void 0===o&&(o=.01);let u=new BloomFilter(n.length,o);for(let o of n)u.add(o);return u}export(){let n={numItems:this.numItems,errorRate:this.errorRate,numBits:this.numBits,numHashes:this.numHashes,bitArray:this.bitArray};return n}import(n){this.numItems=n.numItems,this.errorRate=n.errorRate,this.numBits=n.numBits,this.numHashes=n.numHashes,this.bitArray=n.bitArray}add(n){let o=this.getHashValues(n);o.forEach(n=>{this.bitArray[n]=1})}contains(n){let o=this.getHashValues(n);return o.every(n=>this.bitArray[n])}getHashValues(n){let o=[];for(let u=1;u<=this.numHashes;u++){let s=murmurhash2(""+n+u)%this.numBits;o.push(s)}return o}constructor(n,o){this.numItems=n,this.errorRate=o,this.numBits=Math.ceil(-(n*Math.log(o))/(Math.log(2)*Math.log(2))),this.numHashes=Math.ceil(this.numBits/n*Math.log(2)),this.bitArray=Array(this.numBits).fill(0)}}},729:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),function(n,o){for(var u in o)Object.defineProperty(n,u,{enumerable:!0,get:o[u]})}(o,{MODERN_BROWSERSLIST_TARGET:function(){return f.default},COMPILER_NAMES:function(){return g},INTERNAL_HEADERS:function(){return _},COMPILER_INDEXES:function(){return y},PHASE_EXPORT:function(){return P},PHASE_PRODUCTION_BUILD:function(){return b},PHASE_PRODUCTION_SERVER:function(){return E},PHASE_DEVELOPMENT_SERVER:function(){return S},PHASE_TEST:function(){return R},PHASE_INFO:function(){return w},PAGES_MANIFEST:function(){return O},APP_PATHS_MANIFEST:function(){return j},APP_PATH_ROUTES_MANIFEST:function(){return A},BUILD_MANIFEST:function(){return C},APP_BUILD_MANIFEST:function(){return M},FUNCTIONS_CONFIG_MANIFEST:function(){return I},SUBRESOURCE_INTEGRITY_MANIFEST:function(){return x},NEXT_FONT_MANIFEST:function(){return L},EXPORT_MARKER:function(){return N},EXPORT_DETAIL:function(){return D},PRERENDER_MANIFEST:function(){return k},ROUTES_MANIFEST:function(){return U},IMAGES_MANIFEST:function(){return F},SERVER_FILES_MANIFEST:function(){return H},DEV_CLIENT_PAGES_MANIFEST:function(){return B},MIDDLEWARE_MANIFEST:function(){return W},DEV_MIDDLEWARE_MANIFEST:function(){return q},REACT_LOADABLE_MANIFEST:function(){return z},FONT_MANIFEST:function(){return G},SERVER_DIRECTORY:function(){return V},CONFIG_FILES:function(){return X},BUILD_ID_FILE:function(){return K},BLOCKED_PAGES:function(){return Y},CLIENT_PUBLIC_FILES_PATH:function(){return Q},CLIENT_STATIC_FILES_PATH:function(){return $},STRING_LITERAL_DROP_BUNDLE:function(){return J},NEXT_BUILTIN_DOCUMENT:function(){return Z},BARREL_OPTIMIZATION_PREFIX:function(){return ee},CLIENT_REFERENCE_MANIFEST:function(){return et},SERVER_REFERENCE_MANIFEST:function(){return er},MIDDLEWARE_BUILD_MANIFEST:function(){return en},MIDDLEWARE_REACT_LOADABLE_MANIFEST:function(){return ea},CLIENT_STATIC_FILES_RUNTIME_MAIN:function(){return eo},CLIENT_STATIC_FILES_RUNTIME_MAIN_APP:function(){return ei},APP_CLIENT_INTERNALS:function(){return el},CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH:function(){return eu},CLIENT_STATIC_FILES_RUNTIME_AMP:function(){return es},CLIENT_STATIC_FILES_RUNTIME_WEBPACK:function(){return ec},CLIENT_STATIC_FILES_RUNTIME_POLYFILLS:function(){return ed},CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL:function(){return ef},EDGE_RUNTIME_WEBPACK:function(){return ep},TEMPORARY_REDIRECT_STATUS:function(){return eh},PERMANENT_REDIRECT_STATUS:function(){return em},STATIC_PROPS_ID:function(){return eg},SERVER_PROPS_ID:function(){return e_},PAGE_SEGMENT_KEY:function(){return ey},GOOGLE_FONT_PROVIDER:function(){return eP},OPTIMIZED_FONT_PROVIDERS:function(){return ev},DEFAULT_SERIF_FONT:function(){return eb},DEFAULT_SANS_SERIF_FONT:function(){return eE},STATIC_STATUS_PAGES:function(){return eS},TRACE_OUTPUT_VERSION:function(){return eR},TURBO_TRACE_DEFAULT_MEMORY_LIMIT:function(){return ew},RSC_MODULE_TYPES:function(){return eO},EDGE_UNSUPPORTED_NODE_APIS:function(){return ej},SYSTEM_ENTRYPOINTS:function(){return eA}});let s=u(3219),f=s._(u(1329)),g={client:"client",server:"server",edgeServer:"edge-server"},_=["x-invoke-path","x-invoke-status","x-invoke-error","x-invoke-query","x-middleware-invoke"],y={[g.client]:0,[g.server]:1,[g.edgeServer]:2},P="phase-export",b="phase-production-build",E="phase-production-server",S="phase-development-server",R="phase-test",w="phase-info",O="pages-manifest.json",j="app-paths-manifest.json",A="app-path-routes-manifest.json",C="build-manifest.json",M="app-build-manifest.json",I="functions-config-manifest.json",x="subresource-integrity-manifest",L="next-font-manifest",N="export-marker.json",D="export-detail.json",k="prerender-manifest.json",U="routes-manifest.json",F="images-manifest.json",H="required-server-files.json",B="_devPagesManifest.json",W="middleware-manifest.json",q="_devMiddlewareManifest.json",z="react-loadable-manifest.json",G="font-manifest.json",V="server",X=["next.config.js","next.config.mjs"],K="BUILD_ID",Y=["/_document","/_app","/_error"],Q="public",$="static",J="__NEXT_DROP_CLIENT_FILE__",Z="__NEXT_BUILTIN_DOCUMENT__",ee="__barrel_optimize__",et="client-reference-manifest",er="server-reference-manifest",en="middleware-build-manifest",ea="middleware-react-loadable-manifest",eo="main",ei=""+eo+"-app",el="app-pages-internals",eu="react-refresh",es="amp",ec="webpack",ed="polyfills",ef=Symbol(ed),ep="edge-runtime-webpack",eh=307,em=308,eg="__N_SSG",e_="__N_SSP",ey="__PAGE__",eP="https://fonts.googleapis.com/",ev=[{url:eP,preconnect:"https://fonts.gstatic.com"},{url:"https://use.typekit.net",preconnect:"https://use.typekit.net"}],eb={name:"Times New Roman",xAvgCharWidth:821,azAvgWidth:854.3953488372093,unitsPerEm:2048},eE={name:"Arial",xAvgCharWidth:904,azAvgWidth:934.5116279069767,unitsPerEm:2048},eS=["/500"],eR=1,ew=6e3,eO={client:"client",server:"server"},ej=["clearImmediate","setImmediate","BroadcastChannel","ByteLengthQueuingStrategy","CompressionStream","CountQueuingStrategy","DecompressionStream","DomException","MessageChannel","MessageEvent","MessagePort","ReadableByteStreamController","ReadableStreamBYOBRequest","ReadableStreamDefaultController","TransformStreamDefaultController","WritableStreamDefaultController"],eA=new Set([eo,eu,es,ei]);("function"==typeof o.default||"object"==typeof o.default&&null!==o.default)&&void 0===o.default.__esModule&&(Object.defineProperty(o.default,"__esModule",{value:!0}),Object.assign(o.default,o),n.exports=o.default)},4125:function(n,o){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"escapeStringRegexp",{enumerable:!0,get:function(){return escapeStringRegexp}});let u=/[|\\{}()[\]^$+*?.-]/,s=/[|\\{}()[\]^$+*?.-]/g;function escapeStringRegexp(n){return u.test(n)?n.replace(s,"\\$&"):n}},7768:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"HeadManagerContext",{enumerable:!0,get:function(){return g}});let s=u(3219),f=s._(u(2784)),g=f.default.createContext({})},46:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),function(n,o){for(var u in o)Object.defineProperty(n,u,{enumerable:!0,get:o[u]})}(o,{defaultHead:function(){return defaultHead},default:function(){return S}});let s=u(3219),f=u(6794),g=f._(u(2784)),_=s._(u(3632)),y=u(5938),P=u(7768),b=u(8926);function defaultHead(n){void 0===n&&(n=!1);let o=[g.default.createElement("meta",{charSet:"utf-8"})];return n||o.push(g.default.createElement("meta",{name:"viewport",content:"width=device-width"})),o}function onlyReactElement(n,o){return"string"==typeof o||"number"==typeof o?n:o.type===g.default.Fragment?n.concat(g.default.Children.toArray(o.props.children).reduce((n,o)=>"string"==typeof o||"number"==typeof o?n:n.concat(o),[])):n.concat(o)}u(9941);let E=["name","httpEquiv","charSet","itemProp"];function unique(){let n=new Set,o=new Set,u=new Set,s={};return f=>{let g=!0,_=!1;if(f.key&&"number"!=typeof f.key&&f.key.indexOf("$")>0){_=!0;let o=f.key.slice(f.key.indexOf("$")+1);n.has(o)?g=!1:n.add(o)}switch(f.type){case"title":case"base":o.has(f.type)?g=!1:o.add(f.type);break;case"meta":for(let n=0,o=E.length;n<o;n++){let o=E[n];if(f.props.hasOwnProperty(o)){if("charSet"===o)u.has(o)?g=!1:u.add(o);else{let n=f.props[o],u=s[o]||new Set;("name"!==o||!_)&&u.has(n)?g=!1:(u.add(n),s[o]=u)}}}}return g}}function reduceComponents(n,o){let{inAmpMode:u}=o;return n.reduce(onlyReactElement,[]).reverse().concat(defaultHead(u).reverse()).filter(unique()).reverse().map((n,o)=>{let s=n.key||o;if(!u&&"link"===n.type&&n.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(o=>n.props.href.startsWith(o))){let o={...n.props||{}};return o["data-href"]=o.href,o.href=void 0,o["data-optimized-fonts"]=!0,g.default.cloneElement(n,o)}return g.default.cloneElement(n,{key:s})})}function Head(n){let{children:o}=n,u=(0,g.useContext)(y.AmpStateContext),s=(0,g.useContext)(P.HeadManagerContext);return g.default.createElement(_.default,{reduceComponentsToState:reduceComponents,headManager:s,inAmpMode:(0,b.isInAmpMode)(u)},o)}let S=Head;("function"==typeof o.default||"object"==typeof o.default&&null!==o.default)&&void 0===o.default.__esModule&&(Object.defineProperty(o.default,"__esModule",{value:!0}),Object.assign(o.default,o),n.exports=o.default)},8197:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),function(n,o){for(var u in o)Object.defineProperty(n,u,{enumerable:!0,get:o[u]})}(o,{SearchParamsContext:function(){return f},PathnameContext:function(){return g},PathParamsContext:function(){return _}});let s=u(2784),f=(0,s.createContext)(null),g=(0,s.createContext)(null),_=(0,s.createContext)(null)},4656:function(n,o){"use strict";function normalizeLocalePath(n,o){let u;let s=n.split("/");return(o||[]).some(o=>!!s[1]&&s[1].toLowerCase()===o.toLowerCase()&&(u=o,s.splice(1,1),n=s.join("/")||"/",!0)),{pathname:n,detectedLocale:u}}Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"normalizeLocalePath",{enumerable:!0,get:function(){return normalizeLocalePath}})},4955:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"ImageConfigContext",{enumerable:!0,get:function(){return _}});let s=u(3219),f=s._(u(2784)),g=u(7599),_=f.default.createContext(g.imageConfigDefault)},7599:function(n,o){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),function(n,o){for(var u in o)Object.defineProperty(n,u,{enumerable:!0,get:o[u]})}(o,{VALID_LOADERS:function(){return u},imageConfigDefault:function(){return s}});let u=["default","imgix","cloudinary","akamai","custom"],s={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",remotePatterns:[],unoptimized:!1}},9942:function(n,o){"use strict";function getObjectClassLabel(n){return Object.prototype.toString.call(n)}function isPlainObject(n){if("[object Object]"!==getObjectClassLabel(n))return!1;let o=Object.getPrototypeOf(n);return null===o||o.hasOwnProperty("isPrototypeOf")}Object.defineProperty(o,"__esModule",{value:!0}),function(n,o){for(var u in o)Object.defineProperty(n,u,{enumerable:!0,get:o[u]})}(o,{getObjectClassLabel:function(){return getObjectClassLabel},isPlainObject:function(){return isPlainObject}})},4600:function(n,o){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),function(n,o){for(var u in o)Object.defineProperty(n,u,{enumerable:!0,get:o[u]})}(o,{NEXT_DYNAMIC_NO_SSR_CODE:function(){return u},throwWithNoSSR:function(){return throwWithNoSSR}});let u="NEXT_DYNAMIC_NO_SSR_CODE";function throwWithNoSSR(){let n=Error(u);throw n.digest=u,n}},3745:function(n,o){"use strict";function mitt(){let n=Object.create(null);return{on(o,u){(n[o]||(n[o]=[])).push(u)},off(o,u){n[o]&&n[o].splice(n[o].indexOf(u)>>>0,1)},emit(o){for(var u=arguments.length,s=Array(u>1?u-1:0),f=1;f<u;f++)s[f-1]=arguments[f];(n[o]||[]).slice().map(n=>{n(...s)})}}}Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"default",{enumerable:!0,get:function(){return mitt}})},1329:function(n){"use strict";n.exports=["chrome 64","edge 79","firefox 67","opera 51","safari 12"]},8902:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"denormalizePagePath",{enumerable:!0,get:function(){return denormalizePagePath}});let s=u(1232),f=u(7932);function denormalizePagePath(n){let o=(0,f.normalizePathSep)(n);return o.startsWith("/index/")&&!(0,s.isDynamicRoute)(o)?o.slice(6):"/index"!==o?o:"/"}},3029:function(n,o){"use strict";function ensureLeadingSlash(n){return n.startsWith("/")?n:"/"+n}Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"ensureLeadingSlash",{enumerable:!0,get:function(){return ensureLeadingSlash}})},7932:function(n,o){"use strict";function normalizePathSep(n){return n.replace(/\\/g,"/")}Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"normalizePathSep",{enumerable:!0,get:function(){return normalizePathSep}})},637:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"RouterContext",{enumerable:!0,get:function(){return g}});let s=u(3219),f=s._(u(2784)),g=f.default.createContext(null)},7178:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),function(n,o){for(var u in o)Object.defineProperty(n,u,{enumerable:!0,get:o[u]})}(o,{adaptForAppRouterInstance:function(){return adaptForAppRouterInstance},adaptForSearchParams:function(){return adaptForSearchParams},adaptForPathParams:function(){return adaptForPathParams},PathnameContextProviderAdapter:function(){return PathnameContextProviderAdapter}});let s=u(6794),f=s._(u(2784)),g=u(8197),_=u(1232),y=u(2391),P=u(8300);function adaptForAppRouterInstance(n){return{back(){n.back()},forward(){n.forward()},refresh(){n.reload()},push(o,u){let{scroll:s}=void 0===u?{}:u;n.push(o,void 0,{scroll:s})},replace(o,u){let{scroll:s}=void 0===u?{}:u;n.replace(o,void 0,{scroll:s})},prefetch(o){n.prefetch(o)}}}function adaptForSearchParams(n){return n.isReady&&n.query?(0,y.asPathToSearchParams)(n.asPath):new URLSearchParams}function adaptForPathParams(n){if(!n.isReady||!n.query)return null;let o={},u=(0,P.getRouteRegex)(n.pathname),s=Object.keys(u.groups);for(let u of s)o[u]=n.query[u];return o}function PathnameContextProviderAdapter(n){let{children:o,router:u,...s}=n,y=(0,f.useRef)(s.isAutoExport),P=(0,f.useMemo)(()=>{let n;let o=y.current;if(o&&(y.current=!1),(0,_.isDynamicRoute)(u.pathname)&&(u.isFallback||o&&!u.isReady))return null;try{n=new URL(u.asPath,"http://f")}catch(n){return"/"}return n.pathname},[u.asPath,u.isFallback,u.isReady,u.pathname]);return f.default.createElement(g.PathnameContext.Provider,{value:P},o)}},7797:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),function(n,o){for(var u in o)Object.defineProperty(n,u,{enumerable:!0,get:o[u]})}(o,{default:function(){return Router},matchesMiddleware:function(){return matchesMiddleware},createKey:function(){return createKey}});let s=u(3219),f=u(6794),g=u(6082),_=u(7159),y=u(2288),P=f._(u(676)),b=u(8902),E=u(4656),S=s._(u(3745)),R=u(1306),w=u(8011),O=u(9823);u(2431);let j=u(9001),A=u(8300),C=u(3833);u(4287);let M=u(891),I=u(2048),x=u(3099),L=u(7786),N=u(5864),D=u(5213),k=u(4785),U=u(9423),F=u(673),H=u(8979),B=u(1530),W=u(4131),q=u(6116),z=u(9302),G=u(9381),V=u(9987);function buildCancellationError(){return Object.assign(Error("Route Cancelled"),{cancelled:!0})}async function matchesMiddleware(n){let o=await Promise.resolve(n.router.pageLoader.getMiddleware());if(!o)return!1;let{pathname:u}=(0,M.parsePath)(n.asPath),s=(0,D.hasBasePath)(u)?(0,L.removeBasePath)(u):u,f=(0,N.addBasePath)((0,I.addLocale)(s,n.locale));return o.some(n=>new RegExp(n.regexp).test(f))}function stripOrigin(n){let o=(0,R.getLocationOrigin)();return n.startsWith(o)?n.substring(o.length):n}function prepareUrlAs(n,o,u){let[s,f]=(0,k.resolveHref)(n,o,!0),g=(0,R.getLocationOrigin)(),_=s.startsWith(g),y=f&&f.startsWith(g);s=stripOrigin(s),f=f?stripOrigin(f):f;let P=_?s:(0,N.addBasePath)(s),b=u?stripOrigin((0,k.resolveHref)(n,u)):f||s;return{url:P,as:y?b:(0,N.addBasePath)(b)}}function resolveDynamicRoute(n,o){let u=(0,g.removeTrailingSlash)((0,b.denormalizePagePath)(n));return"/404"===u||"/_error"===u?n:(o.includes(u)||o.some(o=>{if((0,w.isDynamicRoute)(o)&&(0,A.getRouteRegex)(o).re.test(u))return n=o,!0}),(0,g.removeTrailingSlash)(n))}function getMiddlewareData(n,o,u){let s={basePath:u.router.basePath,i18n:{locales:u.router.locales},trailingSlash:!1},f=o.headers.get("x-nextjs-rewrite"),y=f||o.headers.get("x-nextjs-matched-path"),P=o.headers.get("x-matched-path");if(!P||y||P.includes("__next_data_catchall")||P.includes("/_error")||P.includes("/404")||(y=P),y){if(y.startsWith("/")){let o=(0,O.parseRelativeUrl)(y),P=(0,F.getNextPathnameInfo)(o.pathname,{nextConfig:s,parseData:!0}),b=(0,g.removeTrailingSlash)(P.pathname);return Promise.all([u.router.pageLoader.getPageList(),(0,_.getClientBuildManifest)()]).then(g=>{let[_,{__rewrites:y}]=g,S=(0,I.addLocale)(P.pathname,P.locale);if((0,w.isDynamicRoute)(S)||!f&&_.includes((0,E.normalizeLocalePath)((0,L.removeBasePath)(S),u.router.locales).pathname)){let u=(0,F.getNextPathnameInfo)((0,O.parseRelativeUrl)(n).pathname,{nextConfig:s,parseData:!0});S=(0,N.addBasePath)(u.pathname),o.pathname=S}if(!_.includes(b)){let n=resolveDynamicRoute(b,_);n!==b&&(b=n)}let R=_.includes(b)?b:resolveDynamicRoute((0,E.normalizeLocalePath)((0,L.removeBasePath)(o.pathname),u.router.locales).pathname,_);if((0,w.isDynamicRoute)(R)){let n=(0,j.getRouteMatcher)((0,A.getRouteRegex)(R))(S);Object.assign(o.query,n||{})}return{type:"rewrite",parsedAs:o,resolvedHref:R}})}let o=(0,M.parsePath)(n),P=(0,H.formatNextPathnameInfo)({...(0,F.getNextPathnameInfo)(o.pathname,{nextConfig:s,parseData:!0}),defaultLocale:u.router.defaultLocale,buildId:""});return Promise.resolve({type:"redirect-external",destination:""+P+o.query+o.hash})}let b=o.headers.get("x-nextjs-redirect");if(b){if(b.startsWith("/")){let n=(0,M.parsePath)(b),o=(0,H.formatNextPathnameInfo)({...(0,F.getNextPathnameInfo)(n.pathname,{nextConfig:s,parseData:!0}),defaultLocale:u.router.defaultLocale,buildId:""});return Promise.resolve({type:"redirect-internal",newAs:""+o+n.query+n.hash,newUrl:""+o+n.query+n.hash})}return Promise.resolve({type:"redirect-external",destination:b})}return Promise.resolve({type:"next"})}async function withMiddlewareEffects(n){let o=await matchesMiddleware(n);if(!o||!n.fetchData)return null;try{let o=await n.fetchData(),u=await getMiddlewareData(o.dataHref,o.response,n);return{dataHref:o.dataHref,json:o.json,response:o.response,text:o.text,cacheKey:o.cacheKey,effect:u}}catch(n){return null}}let X=Symbol("SSG_DATA_NOT_FOUND");function fetchRetry(n,o,u){return fetch(n,{credentials:"same-origin",method:u.method||"GET",headers:Object.assign({},u.headers,{"x-nextjs-data":"1"})}).then(s=>!s.ok&&o>1&&s.status>=500?fetchRetry(n,o-1,u):s)}function tryToParseAsJSON(n){try{return JSON.parse(n)}catch(n){return null}}function fetchNextData(n){var o;let{dataHref:u,inflightCache:s,isPrefetch:f,hasMiddleware:g,isServerRender:y,parseJSON:P,persistCache:b,isBackground:E,unstable_skipClientCache:S}=n,{href:R}=new URL(u,window.location.href),getData=n=>fetchRetry(u,y?3:1,{headers:Object.assign({},f?{purpose:"prefetch"}:{},f&&g?{"x-middleware-prefetch":"1"}:{}),method:null!=(o=null==n?void 0:n.method)?o:"GET"}).then(o=>o.ok&&(null==n?void 0:n.method)==="HEAD"?{dataHref:u,response:o,text:"",json:{},cacheKey:R}:o.text().then(n=>{if(!o.ok){if(g&&[301,302,307,308].includes(o.status))return{dataHref:u,response:o,text:n,json:{},cacheKey:R};if(404===o.status){var s;if(null==(s=tryToParseAsJSON(n))?void 0:s.notFound)return{dataHref:u,json:{notFound:X},response:o,text:n,cacheKey:R}}let f=Error("Failed to load static props");throw y||(0,_.markAssetError)(f),f}return{dataHref:u,json:P?tryToParseAsJSON(n):null,response:o,text:n,cacheKey:R}})).then(n=>(b&&"no-cache"!==n.response.headers.get("x-middleware-cache")||delete s[R],n)).catch(n=>{throw S||delete s[R],("Failed to fetch"===n.message||"NetworkError when attempting to fetch resource."===n.message||"Load failed"===n.message)&&(0,_.markAssetError)(n),n});return S&&b?getData({}).then(n=>(s[R]=Promise.resolve(n),n)):void 0!==s[R]?s[R]:s[R]=getData(E?{method:"HEAD"}:{})}function createKey(){return Math.random().toString(36).slice(2,10)}function handleHardNavigation(n){let{url:o,router:u}=n;if(o===(0,N.addBasePath)((0,I.addLocale)(u.asPath,u.locale)))throw Error("Invariant: attempted to hard navigate to the same URL "+o+" "+location.href);window.location.href=o}let getCancelledHandler=n=>{let{route:o,router:u}=n,s=!1,f=u.clc=()=>{s=!0};return()=>{if(s){let n=Error('Abort fetching component for route: "'+o+'"');throw n.cancelled=!0,n}f===u.clc&&(u.clc=null)}};let Router=class Router{reload(){window.location.reload()}back(){window.history.back()}forward(){window.history.forward()}push(n,o,u){return void 0===u&&(u={}),{url:n,as:o}=prepareUrlAs(this,n,o),this.change("pushState",n,o,u)}replace(n,o,u){return void 0===u&&(u={}),{url:n,as:o}=prepareUrlAs(this,n,o),this.change("replaceState",n,o,u)}async _bfl(n,o,u,s){{let P=!1,b=!1;for(let E of[n,o])if(E){let o=(0,g.removeTrailingSlash)(new URL(E,"http://n").pathname),S=(0,N.addBasePath)((0,I.addLocale)(o,u||this.locale));if(o!==(0,g.removeTrailingSlash)(new URL(this.asPath,"http://n").pathname)){var f,_,y;for(let n of(P=P||!!(null==(f=this._bfl_s)?void 0:f.contains(o))||!!(null==(_=this._bfl_s)?void 0:_.contains(S)),[o,S])){let o=n.split("/");for(let n=0;!b&&n<o.length+1;n++){let u=o.slice(0,n).join("/");if(u&&(null==(y=this._bfl_d)?void 0:y.contains(u))){b=!0;break}}}if(P||b){if(s)return!0;return handleHardNavigation({url:(0,N.addBasePath)((0,I.addLocale)(n,u||this.locale,this.defaultLocale)),router:this}),new Promise(()=>{})}}}}return!1}async change(n,o,u,s,f){var b,E,S,k,U,F,H,q,V;let K,Y;if(!(0,W.isLocalURL)(o))return handleHardNavigation({url:o,router:this}),!1;let Q=1===s._h;Q||s.shallow||await this._bfl(u,void 0,s.locale);let $=Q||s._shouldResolveHref||(0,M.parsePath)(o).pathname===(0,M.parsePath)(u).pathname,J={...this.state},Z=!0!==this.isReady;this.isReady=!0;let ee=this.isSsr;if(Q||(this.isSsr=!1),Q&&this.clc)return!1;let et=J.locale;R.ST&&performance.mark("routeChange");let{shallow:er=!1,scroll:en=!0}=s,ea={shallow:er};this._inFlightRoute&&this.clc&&(ee||Router.events.emit("routeChangeError",buildCancellationError(),this._inFlightRoute,ea),this.clc(),this.clc=null),u=(0,N.addBasePath)((0,I.addLocale)((0,D.hasBasePath)(u)?(0,L.removeBasePath)(u):u,s.locale,this.defaultLocale));let eo=(0,x.removeLocale)((0,D.hasBasePath)(u)?(0,L.removeBasePath)(u):u,J.locale);this._inFlightRoute=u;let ei=et!==J.locale;if(!Q&&this.onlyAHashChange(eo)&&!ei){J.asPath=eo,Router.events.emit("hashChangeStart",u,ea),this.changeState(n,o,u,{...s,scroll:!1}),en&&this.scrollToHash(eo);try{await this.set(J,this.components[J.route],null)}catch(n){throw(0,P.default)(n)&&n.cancelled&&Router.events.emit("routeChangeError",n,eo,ea),n}return Router.events.emit("hashChangeComplete",u,ea),!0}let el=(0,O.parseRelativeUrl)(o),{pathname:eu,query:es}=el;if(null==(b=this.components[eu])?void 0:b.__appRouter)return handleHardNavigation({url:u,router:this}),new Promise(()=>{});try{[K,{__rewrites:Y}]=await Promise.all([this.pageLoader.getPageList(),(0,_.getClientBuildManifest)(),this.pageLoader.getMiddleware()])}catch(n){return handleHardNavigation({url:u,router:this}),!1}this.urlIsNew(eo)||ei||(n="replaceState");let ec=u;eu=eu?(0,g.removeTrailingSlash)((0,L.removeBasePath)(eu)):eu;let ed=(0,g.removeTrailingSlash)(eu),ef=u.startsWith("/")&&(0,O.parseRelativeUrl)(u).pathname,ep=!!(ef&&ed!==ef&&(!(0,w.isDynamicRoute)(ed)||!(0,j.getRouteMatcher)((0,A.getRouteRegex)(ed))(ef))),eh=!s.shallow&&await matchesMiddleware({asPath:u,locale:J.locale,router:this});if(Q&&eh&&($=!1),$&&"/_error"!==eu&&(s._shouldResolveHref=!0,el.pathname=resolveDynamicRoute(eu,K),el.pathname===eu||(eu=el.pathname,el.pathname=(0,N.addBasePath)(eu),eh||(o=(0,C.formatWithValidation)(el)))),!(0,W.isLocalURL)(u))return handleHardNavigation({url:u,router:this}),!1;ec=(0,x.removeLocale)((0,L.removeBasePath)(ec),J.locale),ed=(0,g.removeTrailingSlash)(eu);let em=!1;if((0,w.isDynamicRoute)(ed)){let n=(0,O.parseRelativeUrl)(ec),s=n.pathname,f=(0,A.getRouteRegex)(ed);em=(0,j.getRouteMatcher)(f)(s);let g=ed===s,_=g?(0,G.interpolateAs)(ed,s,es):{};if(em&&(!g||_.result))g?u=(0,C.formatWithValidation)(Object.assign({},n,{pathname:_.result,query:(0,z.omit)(es,_.params)})):Object.assign(es,em);else{let n=Object.keys(f.groups).filter(n=>!es[n]&&!f.groups[n].optional);if(n.length>0&&!eh)throw Error((g?"The provided `href` ("+o+") value is missing query values ("+n.join(", ")+") to be interpolated properly. ":"The provided `as` value ("+s+") is incompatible with the `href` value ("+ed+"). ")+"Read more: https://nextjs.org/docs/messages/"+(g?"href-interpolation-failed":"incompatible-href-as"))}}Q||Router.events.emit("routeChangeStart",u,ea);let eg="/404"===this.pathname||"/_error"===this.pathname;try{let g=await this.getRouteInfo({route:ed,pathname:eu,query:es,as:u,resolvedAs:ec,routeProps:ea,locale:J.locale,isPreview:J.isPreview,hasMiddleware:eh,unstable_skipClientCache:s.unstable_skipClientCache,isQueryUpdating:Q&&!this.isFallback,isMiddlewareRewrite:ep});if(Q||s.shallow||await this._bfl(u,"resolvedAs"in g?g.resolvedAs:void 0,J.locale),"route"in g&&eh){ed=eu=g.route||ed,ea.shallow||(es=Object.assign({},g.query||{},es));let n=(0,D.hasBasePath)(el.pathname)?(0,L.removeBasePath)(el.pathname):el.pathname;if(em&&eu!==n&&Object.keys(em).forEach(n=>{em&&es[n]===em[n]&&delete es[n]}),(0,w.isDynamicRoute)(eu)){let n=!ea.shallow&&g.resolvedAs?g.resolvedAs:(0,N.addBasePath)((0,I.addLocale)(new URL(u,location.href).pathname,J.locale),!0),o=n;(0,D.hasBasePath)(o)&&(o=(0,L.removeBasePath)(o));let s=(0,A.getRouteRegex)(eu),f=(0,j.getRouteMatcher)(s)(new URL(o,location.href).pathname);f&&Object.assign(es,f)}}if("type"in g){if("redirect-internal"===g.type)return this.change(n,g.newUrl,g.newAs,s);return handleHardNavigation({url:g.destination,router:this}),new Promise(()=>{})}let _=g.Component;if(_&&_.unstable_scriptLoader){let n=[].concat(_.unstable_scriptLoader());n.forEach(n=>{(0,y.handleClientScriptLoad)(n.props)})}if((g.__N_SSG||g.__N_SSP)&&g.props){if(g.props.pageProps&&g.props.pageProps.__N_REDIRECT){s.locale=!1;let o=g.props.pageProps.__N_REDIRECT;if(o.startsWith("/")&&!1!==g.props.pageProps.__N_REDIRECT_BASE_PATH){let u=(0,O.parseRelativeUrl)(o);u.pathname=resolveDynamicRoute(u.pathname,K);let{url:f,as:g}=prepareUrlAs(this,o,o);return this.change(n,f,g,s)}return handleHardNavigation({url:o,router:this}),new Promise(()=>{})}if(J.isPreview=!!g.props.__N_PREVIEW,g.props.notFound===X){let n;try{await this.fetchComponent("/404"),n="/404"}catch(o){n="/_error"}if(g=await this.getRouteInfo({route:n,pathname:n,query:es,as:u,resolvedAs:ec,routeProps:{shallow:!1},locale:J.locale,isPreview:J.isPreview,isNotFound:!0}),"type"in g)throw Error("Unexpected middleware effect on /404")}}Q&&"/_error"===this.pathname&&(null==(S=self.__NEXT_DATA__.props)?void 0:null==(E=S.pageProps)?void 0:E.statusCode)===500&&(null==(k=g.props)?void 0:k.pageProps)&&(g.props.pageProps.statusCode=500);let b=s.shallow&&J.route===(null!=(U=g.route)?U:ed),R=null!=(F=s.scroll)?F:!Q&&!b,C=null!=f?f:R?{x:0,y:0}:null,M={...J,route:ed,pathname:eu,query:es,asPath:eo,isFallback:!1};if(Q&&eg){if(g=await this.getRouteInfo({route:this.pathname,pathname:this.pathname,query:es,as:u,resolvedAs:ec,routeProps:{shallow:!1},locale:J.locale,isPreview:J.isPreview,isQueryUpdating:Q&&!this.isFallback}),"type"in g)throw Error("Unexpected middleware effect on "+this.pathname);"/_error"===this.pathname&&(null==(q=self.__NEXT_DATA__.props)?void 0:null==(H=q.pageProps)?void 0:H.statusCode)===500&&(null==(V=g.props)?void 0:V.pageProps)&&(g.props.pageProps.statusCode=500);try{await this.set(M,g,C)}catch(n){throw(0,P.default)(n)&&n.cancelled&&Router.events.emit("routeChangeError",n,eo,ea),n}return!0}Router.events.emit("beforeHistoryChange",u,ea),this.changeState(n,o,u,s);let x=Q&&!C&&!Z&&!ei&&(0,B.compareRouterStates)(M,this.state);if(!x){try{await this.set(M,g,C)}catch(n){if(n.cancelled)g.error=g.error||n;else throw n}if(g.error)throw Q||Router.events.emit("routeChangeError",g.error,eo,ea),g.error;Q||Router.events.emit("routeChangeComplete",u,ea),R&&/#.+$/.test(u)&&this.scrollToHash(u)}return!0}catch(n){if((0,P.default)(n)&&n.cancelled)return!1;throw n}}changeState(n,o,u,s){void 0===s&&(s={}),("pushState"!==n||(0,R.getURL)()!==u)&&(this._shallow=s.shallow,window.history[n]({url:o,as:u,options:s,__N:!0,key:this._key="pushState"!==n?this._key:createKey()},"",u))}async handleRouteInfoError(n,o,u,s,f,g){if(console.error(n),n.cancelled)throw n;if((0,_.isAssetError)(n)||g)throw Router.events.emit("routeChangeError",n,s,f),handleHardNavigation({url:s,router:this}),buildCancellationError();try{let s;let{page:f,styleSheets:g}=await this.fetchComponent("/_error"),_={props:s,Component:f,styleSheets:g,err:n,error:n};if(!_.props)try{_.props=await this.getInitialProps(f,{err:n,pathname:o,query:u})}catch(n){console.error("Error in error page `getInitialProps`: ",n),_.props={}}return _}catch(n){return this.handleRouteInfoError((0,P.default)(n)?n:Error(n+""),o,u,s,f,!0)}}async getRouteInfo(n){let{route:o,pathname:u,query:s,as:f,resolvedAs:_,routeProps:y,locale:b,hasMiddleware:S,isPreview:R,unstable_skipClientCache:w,isQueryUpdating:O,isMiddlewareRewrite:j,isNotFound:A}=n,M=o;try{var I,x,N,D;let n=getCancelledHandler({route:M,router:this}),o=this.components[M];if(y.shallow&&o&&this.route===M)return o;S&&(o=void 0);let P=!o||"initial"in o?void 0:o,k={dataHref:this.pageLoader.getDataHref({href:(0,C.formatWithValidation)({pathname:u,query:s}),skipInterpolation:!0,asPath:A?"/404":_,locale:b}),hasMiddleware:!0,isServerRender:this.isSsr,parseJSON:!0,inflightCache:O?this.sbc:this.sdc,persistCache:!R,isPrefetch:!1,unstable_skipClientCache:w,isBackground:O},F=O&&!j?null:await withMiddlewareEffects({fetchData:()=>fetchNextData(k),asPath:A?"/404":_,locale:b,router:this}).catch(n=>{if(O)return null;throw n});if(F&&("/_error"===u||"/404"===u)&&(F.effect=void 0),O&&(F?F.json=self.__NEXT_DATA__.props:F={json:self.__NEXT_DATA__.props}),n(),(null==F?void 0:null==(I=F.effect)?void 0:I.type)==="redirect-internal"||(null==F?void 0:null==(x=F.effect)?void 0:x.type)==="redirect-external")return F.effect;if((null==F?void 0:null==(N=F.effect)?void 0:N.type)==="rewrite"){let n=(0,g.removeTrailingSlash)(F.effect.resolvedHref),f=await this.pageLoader.getPageList();if((!O||f.includes(n))&&(M=n,u=F.effect.resolvedHref,s={...s,...F.effect.parsedAs.query},_=(0,L.removeBasePath)((0,E.normalizeLocalePath)(F.effect.parsedAs.pathname,this.locales).pathname),o=this.components[M],y.shallow&&o&&this.route===M&&!S))return{...o,route:M}}if((0,U.isAPIRoute)(M))return handleHardNavigation({url:f,router:this}),new Promise(()=>{});let H=P||await this.fetchComponent(M).then(n=>({Component:n.page,styleSheets:n.styleSheets,__N_SSG:n.mod.__N_SSG,__N_SSP:n.mod.__N_SSP})),B=null==F?void 0:null==(D=F.response)?void 0:D.headers.get("x-middleware-skip"),W=H.__N_SSG||H.__N_SSP;B&&(null==F?void 0:F.dataHref)&&delete this.sdc[F.dataHref];let{props:q,cacheKey:z}=await this._getData(async()=>{if(W){if((null==F?void 0:F.json)&&!B)return{cacheKey:F.cacheKey,props:F.json};let n=(null==F?void 0:F.dataHref)?F.dataHref:this.pageLoader.getDataHref({href:(0,C.formatWithValidation)({pathname:u,query:s}),asPath:_,locale:b}),o=await fetchNextData({dataHref:n,isServerRender:this.isSsr,parseJSON:!0,inflightCache:B?{}:this.sdc,persistCache:!R,isPrefetch:!1,unstable_skipClientCache:w});return{cacheKey:o.cacheKey,props:o.json||{}}}return{headers:{},props:await this.getInitialProps(H.Component,{pathname:u,query:s,asPath:f,locale:b,locales:this.locales,defaultLocale:this.defaultLocale})}});return H.__N_SSP&&k.dataHref&&z&&delete this.sdc[z],this.isPreview||!H.__N_SSG||O||fetchNextData(Object.assign({},k,{isBackground:!0,persistCache:!1,inflightCache:this.sbc})).catch(()=>{}),q.pageProps=Object.assign({},q.pageProps),H.props=q,H.route=M,H.query=s,H.resolvedAs=_,this.components[M]=H,H}catch(n){return this.handleRouteInfoError((0,P.getProperError)(n),u,s,f,y)}}set(n,o,u){return this.state=n,this.sub(o,this.components["/_app"].Component,u)}beforePopState(n){this._bps=n}onlyAHashChange(n){if(!this.asPath)return!1;let[o,u]=this.asPath.split("#",2),[s,f]=n.split("#",2);return!!f&&o===s&&u===f||o===s&&u!==f}scrollToHash(n){let[,o=""]=n.split("#",2);(0,V.handleSmoothScroll)(()=>{if(""===o||"top"===o){window.scrollTo(0,0);return}let n=decodeURIComponent(o),u=document.getElementById(n);if(u){u.scrollIntoView();return}let s=document.getElementsByName(n)[0];s&&s.scrollIntoView()},{onlyHashChange:this.onlyAHashChange(n)})}urlIsNew(n){return this.asPath!==n}async prefetch(n,o,u){if(void 0===o&&(o=n),void 0===u&&(u={}),(0,q.isBot)(window.navigator.userAgent))return;let s=(0,O.parseRelativeUrl)(n),f=s.pathname,{pathname:_,query:y}=s,P=_,b=await this.pageLoader.getPageList(),E=o,S=void 0!==u.locale?u.locale||void 0:this.locale,R=await matchesMiddleware({asPath:o,locale:S,router:this});s.pathname=resolveDynamicRoute(s.pathname,b),(0,w.isDynamicRoute)(s.pathname)&&(_=s.pathname,s.pathname=_,Object.assign(y,(0,j.getRouteMatcher)((0,A.getRouteRegex)(s.pathname))((0,M.parsePath)(o).pathname)||{}),R||(n=(0,C.formatWithValidation)(s)));let I=await withMiddlewareEffects({fetchData:()=>fetchNextData({dataHref:this.pageLoader.getDataHref({href:(0,C.formatWithValidation)({pathname:P,query:y}),skipInterpolation:!0,asPath:E,locale:S}),hasMiddleware:!0,isServerRender:this.isSsr,parseJSON:!0,inflightCache:this.sdc,persistCache:!this.isPreview,isPrefetch:!0}),asPath:o,locale:S,router:this});if((null==I?void 0:I.effect.type)==="rewrite"&&(s.pathname=I.effect.resolvedHref,_=I.effect.resolvedHref,y={...y,...I.effect.parsedAs.query},E=I.effect.parsedAs.pathname,n=(0,C.formatWithValidation)(s)),(null==I?void 0:I.effect.type)==="redirect-external")return;let x=(0,g.removeTrailingSlash)(_);await this._bfl(o,E,u.locale,!0)&&(this.components[f]={__appRouter:!0}),await Promise.all([this.pageLoader._isSsg(x).then(o=>!!o&&fetchNextData({dataHref:(null==I?void 0:I.json)?null==I?void 0:I.dataHref:this.pageLoader.getDataHref({href:n,asPath:E,locale:S}),isServerRender:!1,parseJSON:!0,inflightCache:this.sdc,persistCache:!this.isPreview,isPrefetch:!0,unstable_skipClientCache:u.unstable_skipClientCache||u.priority&&!0}).then(()=>!1).catch(()=>!1)),this.pageLoader[u.priority?"loadPage":"prefetch"](x)])}async fetchComponent(n){let o=getCancelledHandler({route:n,router:this});try{let u=await this.pageLoader.loadPage(n);return o(),u}catch(n){throw o(),n}}_getData(n){let o=!1,cancel=()=>{o=!0};return this.clc=cancel,n().then(n=>{if(cancel===this.clc&&(this.clc=null),o){let n=Error("Loading initial props cancelled");throw n.cancelled=!0,n}return n})}_getFlightData(n){return fetchNextData({dataHref:n,isServerRender:!0,parseJSON:!1,inflightCache:this.sdc,persistCache:!1,isPrefetch:!1}).then(n=>{let{text:o}=n;return{data:o}})}getInitialProps(n,o){let{Component:u}=this.components["/_app"],s=this._wrapApp(u);return o.AppTree=s,(0,R.loadGetInitialProps)(u,{AppTree:s,Component:n,router:this,ctx:o})}get route(){return this.state.route}get pathname(){return this.state.pathname}get query(){return this.state.query}get asPath(){return this.state.asPath}get locale(){return this.state.locale}get isFallback(){return this.state.isFallback}get isPreview(){return this.state.isPreview}constructor(n,o,s,{initialProps:f,pageLoader:_,App:y,wrapApp:P,Component:b,err:E,subscription:S,isFallback:j,locale:A,locales:M,defaultLocale:I,domainLocales:x,isPreview:L}){this.sdc={},this.sbc={},this.isFirstPopStateEvent=!0,this._key=createKey(),this.onPopState=n=>{let o;let{isFirstPopStateEvent:u}=this;this.isFirstPopStateEvent=!1;let s=n.state;if(!s){let{pathname:n,query:o}=this;this.changeState("replaceState",(0,C.formatWithValidation)({pathname:(0,N.addBasePath)(n),query:o}),(0,R.getURL)());return}if(s.__NA){window.location.reload();return}if(!s.__N||u&&this.locale===s.options.locale&&s.as===this.asPath)return;let{url:f,as:g,options:_,key:y}=s;this._key=y;let{pathname:P}=(0,O.parseRelativeUrl)(f);(!this.isSsr||g!==(0,N.addBasePath)(this.asPath)||P!==(0,N.addBasePath)(this.pathname))&&(!this._bps||this._bps(s))&&this.change("replaceState",f,g,Object.assign({},_,{shallow:_.shallow&&this._shallow,locale:_.locale||this.defaultLocale,_h:0}),o)};let D=(0,g.removeTrailingSlash)(n);this.components={},"/_error"!==n&&(this.components[D]={Component:b,initial:!0,props:f,err:E,__N_SSG:f&&f.__N_SSG,__N_SSP:f&&f.__N_SSP}),this.components["/_app"]={Component:y,styleSheets:[]};{let{BloomFilter:n}=u(1403),o={numItems:3,errorRate:.01,numBits:29,numHashes:7,bitArray:[0,0,1,1,0,1,1,1,0,1,1,1,0,1,1,0,0,0,1,1,1,0,1,0,1,0,0,0,1]},s={numItems:0,errorRate:.01,numBits:0,numHashes:null,bitArray:[]};(null==o?void 0:o.numHashes)&&(this._bfl_s=new n(o.numItems,o.errorRate),this._bfl_s.import(o)),(null==s?void 0:s.numHashes)&&(this._bfl_d=new n(s.numItems,s.errorRate),this._bfl_d.import(s))}this.events=Router.events,this.pageLoader=_;let k=(0,w.isDynamicRoute)(n)&&self.__NEXT_DATA__.autoExport;if(this.basePath="",this.sub=S,this.clc=null,this._wrapApp=P,this.isSsr=!0,this.isLocaleDomain=!1,this.isReady=!!(self.__NEXT_DATA__.gssp||self.__NEXT_DATA__.gip||self.__NEXT_DATA__.isExperimentalCompile||self.__NEXT_DATA__.appGip&&!self.__NEXT_DATA__.gsp||!k&&!self.location.search),this.state={route:D,pathname:n,query:o,asPath:k?n:s,isPreview:!!L,locale:void 0,isFallback:j},this._initialMatchesMiddlewarePromise=Promise.resolve(!1),!s.startsWith("//")){let u={locale:A},f=(0,R.getURL)();this._initialMatchesMiddlewarePromise=matchesMiddleware({router:this,locale:A,asPath:f}).then(g=>(u._shouldResolveHref=s!==n,this.changeState("replaceState",g?f:(0,C.formatWithValidation)({pathname:(0,N.addBasePath)(n),query:o}),f,u),g))}window.addEventListener("popstate",this.onPopState)}};Router.events=(0,S.default)()},142:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"addLocale",{enumerable:!0,get:function(){return addLocale}});let s=u(3776),f=u(6315);function addLocale(n,o,u,g){if(!o||o===u)return n;let _=n.toLowerCase();return!g&&((0,f.pathHasPrefix)(_,"/api")||(0,f.pathHasPrefix)(_,"/"+o.toLowerCase()))?n:(0,s.addPathPrefix)(n,"/"+o)}},3776:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"addPathPrefix",{enumerable:!0,get:function(){return addPathPrefix}});let s=u(891);function addPathPrefix(n,o){if(!n.startsWith("/")||!o)return n;let{pathname:u,query:f,hash:g}=(0,s.parsePath)(n);return""+o+u+f+g}},93:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"addPathSuffix",{enumerable:!0,get:function(){return addPathSuffix}});let s=u(891);function addPathSuffix(n,o){if(!n.startsWith("/")||!o)return n;let{pathname:u,query:f,hash:g}=(0,s.parsePath)(n);return""+u+o+f+g}},9537:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),function(n,o){for(var u in o)Object.defineProperty(n,u,{enumerable:!0,get:o[u]})}(o,{normalizeAppPath:function(){return normalizeAppPath},normalizeRscURL:function(){return normalizeRscURL},normalizePostponedURL:function(){return normalizePostponedURL}});let s=u(3029),f=u(7494),g=u(1987);function normalizeAppPath(n){return(0,s.ensureLeadingSlash)(n.split("/").reduce((n,o,u,s)=>!o||(0,f.isGroupSegment)(o)||"@"===o[0]||("page"===o||"route"===o)&&u===s.length-1?n:n+"/"+o,""))}function normalizeRscURL(n){return n.replace(/\.rsc($|\?)/,"$1")}function normalizePostponedURL(n){let o=(0,g.parse)(n),{pathname:u}=o;return u&&u.startsWith("/_next/postponed")?(u=u.substring(16)||"/",(0,g.format)({...o,pathname:u})):n}},2391:function(n,o){"use strict";function asPathToSearchParams(n){return new URL(n,"http://n").searchParams}Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"asPathToSearchParams",{enumerable:!0,get:function(){return asPathToSearchParams}})},1530:function(n,o){"use strict";function compareRouterStates(n,o){let u=Object.keys(n);if(u.length!==Object.keys(o).length)return!1;for(let s=u.length;s--;){let f=u[s];if("query"===f){let u=Object.keys(n.query);if(u.length!==Object.keys(o.query).length)return!1;for(let s=u.length;s--;){let f=u[s];if(!o.query.hasOwnProperty(f)||n.query[f]!==o.query[f])return!1}}else if(!o.hasOwnProperty(f)||n[f]!==o[f])return!1}return!0}Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"compareRouterStates",{enumerable:!0,get:function(){return compareRouterStates}})},8979:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"formatNextPathnameInfo",{enumerable:!0,get:function(){return formatNextPathnameInfo}});let s=u(6082),f=u(3776),g=u(93),_=u(142);function formatNextPathnameInfo(n){let o=(0,_.addLocale)(n.pathname,n.locale,n.buildId?void 0:n.defaultLocale,n.ignorePrefix);return(n.buildId||!n.trailingSlash)&&(o=(0,s.removeTrailingSlash)(o)),n.buildId&&(o=(0,g.addPathSuffix)((0,f.addPathPrefix)(o,"/_next/data/"+n.buildId),"/"===n.pathname?"index.json":".json")),o=(0,f.addPathPrefix)(o,n.basePath),!n.buildId&&n.trailingSlash?o.endsWith("/")?o:(0,g.addPathSuffix)(o,"/"):(0,s.removeTrailingSlash)(o)}},3833:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),function(n,o){for(var u in o)Object.defineProperty(n,u,{enumerable:!0,get:o[u]})}(o,{formatUrl:function(){return formatUrl},urlObjectKeys:function(){return _},formatWithValidation:function(){return formatWithValidation}});let s=u(6794),f=s._(u(4828)),g=/https?|ftp|gopher|file/;function formatUrl(n){let{auth:o,hostname:u}=n,s=n.protocol||"",_=n.pathname||"",y=n.hash||"",P=n.query||"",b=!1;o=o?encodeURIComponent(o).replace(/%3A/i,":")+"@":"",n.host?b=o+n.host:u&&(b=o+(~u.indexOf(":")?"["+u+"]":u),n.port&&(b+=":"+n.port)),P&&"object"==typeof P&&(P=String(f.urlQueryToSearchParams(P)));let E=n.search||P&&"?"+P||"";return s&&!s.endsWith(":")&&(s+=":"),n.slashes||(!s||g.test(s))&&!1!==b?(b="//"+(b||""),_&&"/"!==_[0]&&(_="/"+_)):b||(b=""),y&&"#"!==y[0]&&(y="#"+y),E&&"?"!==E[0]&&(E="?"+E),""+s+b+(_=_.replace(/[?#]/g,encodeURIComponent))+(E=E.replace("#","%23"))+y}let _=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function formatWithValidation(n){return formatUrl(n)}},8052:function(n,o){"use strict";function getAssetPathFromRoute(n,o){void 0===o&&(o="");let u="/"===n?"/index":/^\/index(\/|$)/.test(n)?"/index"+n:""+n;return u+o}Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"default",{enumerable:!0,get:function(){return getAssetPathFromRoute}})},673:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"getNextPathnameInfo",{enumerable:!0,get:function(){return getNextPathnameInfo}});let s=u(4656),f=u(4980),g=u(6315);function getNextPathnameInfo(n,o){var u,_;let{basePath:y,i18n:P,trailingSlash:b}=null!=(u=o.nextConfig)?u:{},E={pathname:n,trailingSlash:"/"!==n?n.endsWith("/"):b};y&&(0,g.pathHasPrefix)(E.pathname,y)&&(E.pathname=(0,f.removePathPrefix)(E.pathname,y),E.basePath=y);let S=E.pathname;if(E.pathname.startsWith("/_next/data/")&&E.pathname.endsWith(".json")){let n=E.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/"),u=n[0];E.buildId=u,S="index"!==n[1]?"/"+n.slice(1).join("/"):"/",!0===o.parseData&&(E.pathname=S)}if(P){let n=o.i18nProvider?o.i18nProvider.analyze(E.pathname):(0,s.normalizeLocalePath)(E.pathname,P.locales);E.locale=n.detectedLocale,E.pathname=null!=(_=n.pathname)?_:E.pathname,!n.detectedLocale&&E.buildId&&(n=o.i18nProvider?o.i18nProvider.analyze(S):(0,s.normalizeLocalePath)(S,P.locales)).detectedLocale&&(E.locale=n.detectedLocale)}return E}},9987:function(n,o){"use strict";function handleSmoothScroll(n,o){if(void 0===o&&(o={}),o.onlyHashChange){n();return}let u=document.documentElement,s=u.style.scrollBehavior;u.style.scrollBehavior="auto",o.dontForceLayout||u.getClientRects(),n(),u.style.scrollBehavior=s}Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"handleSmoothScroll",{enumerable:!0,get:function(){return handleSmoothScroll}})},1232:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),function(n,o){for(var u in o)Object.defineProperty(n,u,{enumerable:!0,get:o[u]})}(o,{getSortedRoutes:function(){return s.getSortedRoutes},isDynamicRoute:function(){return f.isDynamicRoute}});let s=u(2839),f=u(8011)},9381:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"interpolateAs",{enumerable:!0,get:function(){return interpolateAs}});let s=u(9001),f=u(8300);function interpolateAs(n,o,u){let g="",_=(0,f.getRouteRegex)(n),y=_.groups,P=(o!==n?(0,s.getRouteMatcher)(_)(o):"")||u;g=n;let b=Object.keys(y);return b.every(n=>{let o=P[n]||"",{repeat:u,optional:s}=y[n],f="["+(u?"...":"")+n+"]";return s&&(f=(o?"":"/")+"["+f+"]"),u&&!Array.isArray(o)&&(o=[o]),(s||n in P)&&(g=g.replace(f,u?o.map(n=>encodeURIComponent(n)).join("/"):encodeURIComponent(o))||"/")})||(g=""),{params:b,result:g}}},6116:function(n,o){"use strict";function isBot(n){return/Googlebot|Mediapartners-Google|AdsBot-Google|googleweblight|Storebot-Google|Google-PageRenderer|Bingbot|BingPreview|Slurp|DuckDuckBot|baiduspider|yandex|sogou|LinkedInBot|bitlybot|tumblr|vkShare|quora link preview|facebookexternalhit|facebookcatalog|Twitterbot|applebot|redditbot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|ia_archiver/i.test(n)}Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"isBot",{enumerable:!0,get:function(){return isBot}})},8011:function(n,o){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"isDynamicRoute",{enumerable:!0,get:function(){return isDynamicRoute}});let u=/\/\[[^/]+?\](?=\/|$)/;function isDynamicRoute(n){return u.test(n)}},4131:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"isLocalURL",{enumerable:!0,get:function(){return isLocalURL}});let s=u(1306),f=u(5213);function isLocalURL(n){if(!(0,s.isAbsoluteUrl)(n))return!0;try{let o=(0,s.getLocationOrigin)(),u=new URL(n,o);return u.origin===o&&(0,f.hasBasePath)(u.pathname)}catch(n){return!1}}},9302:function(n,o){"use strict";function omit(n,o){let u={};return Object.keys(n).forEach(s=>{o.includes(s)||(u[s]=n[s])}),u}Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"omit",{enumerable:!0,get:function(){return omit}})},891:function(n,o){"use strict";function parsePath(n){let o=n.indexOf("#"),u=n.indexOf("?"),s=u>-1&&(o<0||u<o);return s||o>-1?{pathname:n.substring(0,s?u:o),query:s?n.substring(u,o>-1?o:void 0):"",hash:o>-1?n.slice(o):""}:{pathname:n,query:"",hash:""}}Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"parsePath",{enumerable:!0,get:function(){return parsePath}})},9823:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"parseRelativeUrl",{enumerable:!0,get:function(){return parseRelativeUrl}});let s=u(1306),f=u(4828);function parseRelativeUrl(n,o){let u=new URL((0,s.getLocationOrigin)()),g=o?new URL(o,u):n.startsWith(".")?new URL(window.location.href):u,{pathname:_,searchParams:y,search:P,hash:b,href:E,origin:S}=new URL(n,g);if(S!==u.origin)throw Error("invariant: invalid relative URL, router received "+n);return{pathname:_,query:(0,f.searchParamsToUrlQuery)(y),search:P,hash:b,href:E.slice(u.origin.length)}}},6315:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"pathHasPrefix",{enumerable:!0,get:function(){return pathHasPrefix}});let s=u(891);function pathHasPrefix(n,o){if("string"!=typeof n)return!1;let{pathname:u}=(0,s.parsePath)(n);return u===o||u.startsWith(o+"/")}},4828:function(n,o){"use strict";function searchParamsToUrlQuery(n){let o={};return n.forEach((n,u)=>{void 0===o[u]?o[u]=n:Array.isArray(o[u])?o[u].push(n):o[u]=[o[u],n]}),o}function stringifyUrlQueryParam(n){return"string"!=typeof n&&("number"!=typeof n||isNaN(n))&&"boolean"!=typeof n?"":String(n)}function urlQueryToSearchParams(n){let o=new URLSearchParams;return Object.entries(n).forEach(n=>{let[u,s]=n;Array.isArray(s)?s.forEach(n=>o.append(u,stringifyUrlQueryParam(n))):o.set(u,stringifyUrlQueryParam(s))}),o}function assign(n){for(var o=arguments.length,u=Array(o>1?o-1:0),s=1;s<o;s++)u[s-1]=arguments[s];return u.forEach(o=>{Array.from(o.keys()).forEach(o=>n.delete(o)),o.forEach((o,u)=>n.append(u,o))}),n}Object.defineProperty(o,"__esModule",{value:!0}),function(n,o){for(var u in o)Object.defineProperty(n,u,{enumerable:!0,get:o[u]})}(o,{searchParamsToUrlQuery:function(){return searchParamsToUrlQuery},urlQueryToSearchParams:function(){return urlQueryToSearchParams},assign:function(){return assign}})},4980:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"removePathPrefix",{enumerable:!0,get:function(){return removePathPrefix}});let s=u(6315);function removePathPrefix(n,o){if(!(0,s.pathHasPrefix)(n,o))return n;let u=n.slice(o.length);return u.startsWith("/")?u:"/"+u}},6082:function(n,o){"use strict";function removeTrailingSlash(n){return n.replace(/\/$/,"")||"/"}Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"removeTrailingSlash",{enumerable:!0,get:function(){return removeTrailingSlash}})},9001:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"getRouteMatcher",{enumerable:!0,get:function(){return getRouteMatcher}});let s=u(1306);function getRouteMatcher(n){let{re:o,groups:u}=n;return n=>{let f=o.exec(n);if(!f)return!1;let decode=n=>{try{return decodeURIComponent(n)}catch(n){throw new s.DecodeError("failed to decode param")}},g={};return Object.keys(u).forEach(n=>{let o=u[n],s=f[o.pos];void 0!==s&&(g[n]=~s.indexOf("/")?s.split("/").map(n=>decode(n)):o.repeat?[decode(s)]:decode(s))}),g}}},8300:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),function(n,o){for(var u in o)Object.defineProperty(n,u,{enumerable:!0,get:o[u]})}(o,{getRouteRegex:function(){return getRouteRegex},getNamedRouteRegex:function(){return getNamedRouteRegex},getNamedMiddlewareRegex:function(){return getNamedMiddlewareRegex}});let s=u(2407),f=u(4125),g=u(6082);function parseParameter(n){let o=n.startsWith("[")&&n.endsWith("]");o&&(n=n.slice(1,-1));let u=n.startsWith("...");return u&&(n=n.slice(3)),{key:n,repeat:u,optional:o}}function getParametrizedRoute(n){let o=(0,g.removeTrailingSlash)(n).slice(1).split("/"),u={},_=1;return{parameterizedRoute:o.map(n=>{let o=s.INTERCEPTION_ROUTE_MARKERS.find(o=>n.startsWith(o)),g=n.match(/\[((?:\[.*\])|.+)\]/);if(o&&g){let{key:n,optional:s,repeat:y}=parseParameter(g[1]);return u[n]={pos:_++,repeat:y,optional:s},"/"+(0,f.escapeStringRegexp)(o)+"([^/]+?)"}if(!g)return"/"+(0,f.escapeStringRegexp)(n);{let{key:n,repeat:o,optional:s}=parseParameter(g[1]);return u[n]={pos:_++,repeat:o,optional:s},o?s?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)"}}).join(""),groups:u}}function getRouteRegex(n){let{parameterizedRoute:o,groups:u}=getParametrizedRoute(n);return{re:RegExp("^"+o+"(?:/)?$"),groups:u}}function buildGetSafeRouteKey(){let n=0;return()=>{let o="",u=++n;for(;u>0;)o+=String.fromCharCode(97+(u-1)%26),u=Math.floor((u-1)/26);return o}}function getSafeKeyFromSegment(n){let{getSafeRouteKey:o,segment:u,routeKeys:s,keyPrefix:f}=n,{key:g,optional:_,repeat:y}=parseParameter(u),P=g.replace(/\W/g,"");f&&(P=""+f+P);let b=!1;return(0===P.length||P.length>30)&&(b=!0),isNaN(parseInt(P.slice(0,1)))||(b=!0),b&&(P=o()),f?s[P]=""+f+g:s[P]=""+g,y?_?"(?:/(?<"+P+">.+?))?":"/(?<"+P+">.+?)":"/(?<"+P+">[^/]+?)"}function getNamedParametrizedRoute(n,o){let u=(0,g.removeTrailingSlash)(n).slice(1).split("/"),_=buildGetSafeRouteKey(),y={};return{namedParameterizedRoute:u.map(n=>{let u=s.INTERCEPTION_ROUTE_MARKERS.some(o=>n.startsWith(o)),g=n.match(/\[((?:\[.*\])|.+)\]/);return u&&g?getSafeKeyFromSegment({getSafeRouteKey:_,segment:g[1],routeKeys:y,keyPrefix:o?"nxtI":void 0}):g?getSafeKeyFromSegment({getSafeRouteKey:_,segment:g[1],routeKeys:y,keyPrefix:o?"nxtP":void 0}):"/"+(0,f.escapeStringRegexp)(n)}).join(""),routeKeys:y}}function getNamedRouteRegex(n,o){let u=getNamedParametrizedRoute(n,o);return{...getRouteRegex(n),namedRegex:"^"+u.namedParameterizedRoute+"(?:/)?$",routeKeys:u.routeKeys}}function getNamedMiddlewareRegex(n,o){let{parameterizedRoute:u}=getParametrizedRoute(n),{catchAll:s=!0}=o;if("/"===u)return{namedRegex:"^/"+(s?".*":"")+"$"};let{namedParameterizedRoute:f}=getNamedParametrizedRoute(n,!1);return{namedRegex:"^"+f+(s?"(?:(/.*)?)":"")+"$"}}},2839:function(n,o){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"getSortedRoutes",{enumerable:!0,get:function(){return getSortedRoutes}});let UrlNode=class UrlNode{insert(n){this._insert(n.split("/").filter(Boolean),[],!1)}smoosh(){return this._smoosh()}_smoosh(n){void 0===n&&(n="/");let o=[...this.children.keys()].sort();null!==this.slugName&&o.splice(o.indexOf("[]"),1),null!==this.restSlugName&&o.splice(o.indexOf("[...]"),1),null!==this.optionalRestSlugName&&o.splice(o.indexOf("[[...]]"),1);let u=o.map(o=>this.children.get(o)._smoosh(""+n+o+"/")).reduce((n,o)=>[...n,...o],[]);if(null!==this.slugName&&u.push(...this.children.get("[]")._smoosh(n+"["+this.slugName+"]/")),!this.placeholder){let o="/"===n?"/":n.slice(0,-1);if(null!=this.optionalRestSlugName)throw Error('You cannot define a route with the same specificity as a optional catch-all route ("'+o+'" and "'+o+"[[..."+this.optionalRestSlugName+']]").');u.unshift(o)}return null!==this.restSlugName&&u.push(...this.children.get("[...]")._smoosh(n+"[..."+this.restSlugName+"]/")),null!==this.optionalRestSlugName&&u.push(...this.children.get("[[...]]")._smoosh(n+"[[..."+this.optionalRestSlugName+"]]/")),u}_insert(n,o,u){if(0===n.length){this.placeholder=!1;return}if(u)throw Error("Catch-all must be the last part of the URL.");let s=n[0];if(s.startsWith("[")&&s.endsWith("]")){let f=s.slice(1,-1),g=!1;if(f.startsWith("[")&&f.endsWith("]")&&(f=f.slice(1,-1),g=!0),f.startsWith("...")&&(f=f.substring(3),u=!0),f.startsWith("[")||f.endsWith("]"))throw Error("Segment names may not start or end with extra brackets ('"+f+"').");if(f.startsWith("."))throw Error("Segment names may not start with erroneous periods ('"+f+"').");function handleSlug(n,u){if(null!==n&&n!==u)throw Error("You cannot use different slug names for the same dynamic path ('"+n+"' !== '"+u+"').");o.forEach(n=>{if(n===u)throw Error('You cannot have the same slug name "'+u+'" repeat within a single dynamic path');if(n.replace(/\W/g,"")===s.replace(/\W/g,""))throw Error('You cannot have the slug names "'+n+'" and "'+u+'" differ only by non-word symbols within a single dynamic path')}),o.push(u)}if(u){if(g){if(null!=this.restSlugName)throw Error('You cannot use both an required and optional catch-all route at the same level ("[...'+this.restSlugName+']" and "'+n[0]+'" ).');handleSlug(this.optionalRestSlugName,f),this.optionalRestSlugName=f,s="[[...]]"}else{if(null!=this.optionalRestSlugName)throw Error('You cannot use both an optional and required catch-all route at the same level ("[[...'+this.optionalRestSlugName+']]" and "'+n[0]+'").');handleSlug(this.restSlugName,f),this.restSlugName=f,s="[...]"}}else{if(g)throw Error('Optional route parameters are not yet supported ("'+n[0]+'").');handleSlug(this.slugName,f),this.slugName=f,s="[]"}}this.children.has(s)||this.children.set(s,new UrlNode),this.children.get(s)._insert(n.slice(1),o,u)}constructor(){this.placeholder=!0,this.children=new Map,this.slugName=null,this.restSlugName=null,this.optionalRestSlugName=null}};function getSortedRoutes(n){let o=new UrlNode;return n.forEach(n=>o.insert(n)),o.smoosh()}},510:function(n,o){"use strict";let u;Object.defineProperty(o,"__esModule",{value:!0}),function(n,o){for(var u in o)Object.defineProperty(n,u,{enumerable:!0,get:o[u]})}(o,{default:function(){return _default},setConfig:function(){return setConfig}});let _default=()=>u;function setConfig(n){u=n}},7494:function(n,o){"use strict";function isGroupSegment(n){return"("===n[0]&&n.endsWith(")")}Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"isGroupSegment",{enumerable:!0,get:function(){return isGroupSegment}})},3632:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"default",{enumerable:!0,get:function(){return SideEffect}});let s=u(2784),f=s.useLayoutEffect,g=s.useEffect;function SideEffect(n){let{headManager:o,reduceComponentsToState:u}=n;function emitChange(){if(o&&o.mountedInstances){let f=s.Children.toArray(Array.from(o.mountedInstances).filter(Boolean));o.updateHead(u(f,n))}}return f(()=>{var u;return null==o||null==(u=o.mountedInstances)||u.add(n.children),()=>{var u;null==o||null==(u=o.mountedInstances)||u.delete(n.children)}}),f(()=>(o&&(o._pendingUpdate=emitChange),()=>{o&&(o._pendingUpdate=emitChange)})),g(()=>(o&&o._pendingUpdate&&(o._pendingUpdate(),o._pendingUpdate=null),()=>{o&&o._pendingUpdate&&(o._pendingUpdate(),o._pendingUpdate=null)})),null}},1306:function(n,o){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),function(n,o){for(var u in o)Object.defineProperty(n,u,{enumerable:!0,get:o[u]})}(o,{WEB_VITALS:function(){return u},execOnce:function(){return execOnce},isAbsoluteUrl:function(){return isAbsoluteUrl},getLocationOrigin:function(){return getLocationOrigin},getURL:function(){return getURL},getDisplayName:function(){return getDisplayName},isResSent:function(){return isResSent},normalizeRepeatedSlashes:function(){return normalizeRepeatedSlashes},loadGetInitialProps:function(){return loadGetInitialProps},SP:function(){return f},ST:function(){return g},DecodeError:function(){return DecodeError},NormalizeError:function(){return NormalizeError},PageNotFoundError:function(){return PageNotFoundError},MissingStaticPage:function(){return MissingStaticPage},MiddlewareNotFoundError:function(){return MiddlewareNotFoundError},stringifyError:function(){return stringifyError}});let u=["CLS","FCP","FID","INP","LCP","TTFB"];function execOnce(n){let o,u=!1;return function(){for(var s=arguments.length,f=Array(s),g=0;g<s;g++)f[g]=arguments[g];return u||(u=!0,o=n(...f)),o}}let s=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,isAbsoluteUrl=n=>s.test(n);function getLocationOrigin(){let{protocol:n,hostname:o,port:u}=window.location;return n+"//"+o+(u?":"+u:"")}function getURL(){let{href:n}=window.location,o=getLocationOrigin();return n.substring(o.length)}function getDisplayName(n){return"string"==typeof n?n:n.displayName||n.name||"Unknown"}function isResSent(n){return n.finished||n.headersSent}function normalizeRepeatedSlashes(n){let o=n.split("?"),u=o[0];return u.replace(/\\/g,"/").replace(/\/\/+/g,"/")+(o[1]?"?"+o.slice(1).join("?"):"")}async function loadGetInitialProps(n,o){let u=o.res||o.ctx&&o.ctx.res;if(!n.getInitialProps)return o.ctx&&o.Component?{pageProps:await loadGetInitialProps(o.Component,o.ctx)}:{};let s=await n.getInitialProps(o);if(u&&isResSent(u))return s;if(!s){let o='"'+getDisplayName(n)+'.getInitialProps()" should resolve to an object. But found "'+s+'" instead.';throw Error(o)}return s}let f="undefined"!=typeof performance,g=f&&["mark","measure","getEntriesByName"].every(n=>"function"==typeof performance[n]);let DecodeError=class DecodeError extends Error{};let NormalizeError=class NormalizeError extends Error{};let PageNotFoundError=class PageNotFoundError extends Error{constructor(n){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+n}};let MissingStaticPage=class MissingStaticPage extends Error{constructor(n,o){super(),this.message="Failed to load static file for page: "+n+" "+o}};let MiddlewareNotFoundError=class MiddlewareNotFoundError extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}};function stringifyError(n){return JSON.stringify({message:n.message,stack:n.stack})}},9941:function(n,o){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"warnOnce",{enumerable:!0,get:function(){return warnOnce}});let warnOnce=n=>{}},1987:function(n,o,u){!function(){var o={452:function(n){"use strict";n.exports=u(7334)}},s={};function __nccwpck_require__(n){var u=s[n];if(void 0!==u)return u.exports;var f=s[n]={exports:{}},g=!0;try{o[n](f,f.exports,__nccwpck_require__),g=!1}finally{g&&delete s[n]}return f.exports}__nccwpck_require__.ab="//";var f={};!function(){var n,o=(n=__nccwpck_require__(452))&&"object"==typeof n&&"default"in n?n.default:n,u=/https?|ftp|gopher|file/;function r(n){"string"==typeof n&&(n=d(n));var s,f,g,_,y,P,b,E,S,R=(f=(s=n).auth,g=s.hostname,_=s.protocol||"",y=s.pathname||"",P=s.hash||"",b=s.query||"",E=!1,f=f?encodeURIComponent(f).replace(/%3A/i,":")+"@":"",s.host?E=f+s.host:g&&(E=f+(~g.indexOf(":")?"["+g+"]":g),s.port&&(E+=":"+s.port)),b&&"object"==typeof b&&(b=o.encode(b)),S=s.search||b&&"?"+b||"",_&&":"!==_.substr(-1)&&(_+=":"),s.slashes||(!_||u.test(_))&&!1!==E?(E="//"+(E||""),y&&"/"!==y[0]&&(y="/"+y)):E||(E=""),P&&"#"!==P[0]&&(P="#"+P),S&&"?"!==S[0]&&(S="?"+S),{protocol:_,host:E,pathname:y=y.replace(/[?#]/g,encodeURIComponent),search:S=S.replace("#","%23"),hash:P});return""+R.protocol+R.host+R.pathname+R.search+R.hash}var s="http://",g=s+"w.w",_=/^([a-z0-9.+-]*:\/\/\/)([a-z0-9.+-]:\/*)?/i,y=/https?|ftp|gopher|file/;function h(n,o){var u="string"==typeof n?d(n):n;n="object"==typeof n?r(n):n;var f=d(o),P="";u.protocol&&!u.slashes&&(P=u.protocol,n=n.replace(u.protocol,""),P+="/"===o[0]||"/"===n[0]?"/":""),P&&f.protocol&&(P="",f.slashes||(P=f.protocol,o=o.replace(f.protocol,"")));var b=n.match(_);b&&!f.protocol&&(n=n.substr((P=b[1]+(b[2]||"")).length),/^\/\/[^/]/.test(o)&&(P=P.slice(0,-1)));var E=new URL(n,g+"/"),S=new URL(o,E).toString().replace(g,""),R=f.protocol||u.protocol;return R+=u.slashes||f.slashes?"//":"",!P&&R?S=S.replace(s,R):P&&(S=S.replace(s,"")),y.test(S)||~o.indexOf(".")||"/"===n.slice(-1)||"/"===o.slice(-1)||"/"!==S.slice(-1)||(S=S.slice(0,-1)),P&&(S=P+("/"===S[0]?S.substr(1):S)),S}function l(){}l.prototype.parse=d,l.prototype.format=r,l.prototype.resolve=h,l.prototype.resolveObject=h;var P=/^https?|ftp|gopher|file/,b=/^(.*?)([#?].*)/,E=/^([a-z0-9.+-]*:)(\/{0,3})(.*)/i,S=/^([a-z0-9.+-]*:)?\/\/\/*/i,R=/^([a-z0-9.+-]*:)(\/{0,2})\[(.*)\]$/i;function d(n,u,s){if(void 0===u&&(u=!1),void 0===s&&(s=!1),n&&"object"==typeof n&&n instanceof l)return n;var f=(n=n.trim()).match(b);n=f?f[1].replace(/\\/g,"/")+f[2]:n.replace(/\\/g,"/"),R.test(n)&&"/"!==n.slice(-1)&&(n+="/");var _=!/(^javascript)/.test(n)&&n.match(E),y=S.test(n),w="";_&&(P.test(_[1])||(w=_[1].toLowerCase(),n=""+_[2]+_[3]),_[2]||(y=!1,P.test(_[1])?(w=_[1],n=""+_[3]):n="//"+_[3]),3!==_[2].length&&1!==_[2].length||(w=_[1],n="/"+_[3]));var O,j=(f?f[1]:n).match(/^https?:\/\/[^/]+(:[0-9]+)(?=\/|$)/),A=j&&j[1],C=new l,M="",I="";try{O=new URL(n)}catch(o){M=o,w||s||!/^\/\//.test(n)||/^\/\/.+[@.]/.test(n)||(I="/",n=n.substr(1));try{O=new URL(n,g)}catch(n){return C.protocol=w,C.href=w,C}}C.slashes=y&&!I,C.host="w.w"===O.host?"":O.host,C.hostname="w.w"===O.hostname?"":O.hostname.replace(/(\[|\])/g,""),C.protocol=M?w||null:O.protocol,C.search=O.search.replace(/\\/g,"%5C"),C.hash=O.hash.replace(/\\/g,"%5C");var x=n.split("#");!C.search&&~x[0].indexOf("?")&&(C.search="?"),C.hash||""!==x[1]||(C.hash="#"),C.query=u?o.decode(O.search.substr(1)):C.search.substr(1),C.pathname=I+(_?O.pathname.replace(/['^|`]/g,function(n){return"%"+n.charCodeAt().toString(16).toUpperCase()}).replace(/((?:%[0-9A-F]{2})+)/g,function(n,o){try{return decodeURIComponent(o).split("").map(function(n){var o=n.charCodeAt();return o>256||/^[a-z0-9]$/i.test(n)?n:"%"+o.toString(16).toUpperCase()}).join("")}catch(n){return o}}):O.pathname),"about:"===C.protocol&&"blank"===C.pathname&&(C.protocol="",C.pathname=""),M&&"/"!==n[0]&&(C.pathname=C.pathname.substr(1)),w&&!P.test(w)&&"/"!==n.slice(-1)&&"/"===C.pathname&&(C.pathname=""),C.path=C.pathname+C.search,C.auth=[O.username,O.password].map(decodeURIComponent).filter(Boolean).join(":"),C.port=O.port,A&&!C.host.endsWith(A)&&(C.host+=A,C.port=A.slice(1)),C.href=I?""+C.pathname+C.search+C.hash:r(C);var L=/^(file)/.test(C.href)?["host","hostname"]:[];return Object.keys(C).forEach(function(n){~L.indexOf(n)||(C[n]=C[n]||null)}),C}f.parse=d,f.format=r,f.resolve=h,f.resolveObject=function(n,o){return d(h(n,o))},f.Url=l}(),n.exports=f}()},7334:function(n){!function(){"use strict";var o={815:function(n){function hasOwnProperty(n,o){return Object.prototype.hasOwnProperty.call(n,o)}n.exports=function(n,u,s,f){u=u||"&",s=s||"=";var g={};if("string"!=typeof n||0===n.length)return g;var _=/\+/g;n=n.split(u);var y=1e3;f&&"number"==typeof f.maxKeys&&(y=f.maxKeys);var P=n.length;y>0&&P>y&&(P=y);for(var b=0;b<P;++b){var E,S,R,w,O=n[b].replace(_,"%20"),j=O.indexOf(s);j>=0?(E=O.substr(0,j),S=O.substr(j+1)):(E=O,S=""),R=decodeURIComponent(E),w=decodeURIComponent(S),hasOwnProperty(g,R)?o(g[R])?g[R].push(w):g[R]=[g[R],w]:g[R]=w}return g};var o=Array.isArray||function(n){return"[object Array]"===Object.prototype.toString.call(n)}},577:function(n){var stringifyPrimitive=function(n){switch(typeof n){case"string":return n;case"boolean":return n?"true":"false";case"number":return isFinite(n)?n:"";default:return""}};n.exports=function(n,s,f,g){return(s=s||"&",f=f||"=",null===n&&(n=void 0),"object"==typeof n)?map(u(n),function(u){var g=encodeURIComponent(stringifyPrimitive(u))+f;return o(n[u])?map(n[u],function(n){return g+encodeURIComponent(stringifyPrimitive(n))}).join(s):g+encodeURIComponent(stringifyPrimitive(n[u]))}).join(s):g?encodeURIComponent(stringifyPrimitive(g))+f+encodeURIComponent(stringifyPrimitive(n)):""};var o=Array.isArray||function(n){return"[object Array]"===Object.prototype.toString.call(n)};function map(n,o){if(n.map)return n.map(o);for(var u=[],s=0;s<n.length;s++)u.push(o(n[s],s));return u}var u=Object.keys||function(n){var o=[];for(var u in n)Object.prototype.hasOwnProperty.call(n,u)&&o.push(u);return o}}},u={};function __nccwpck_require__(n){var s=u[n];if(void 0!==s)return s.exports;var f=u[n]={exports:{}},g=!0;try{o[n](f,f.exports,__nccwpck_require__),g=!1}finally{g&&delete u[n]}return f.exports}__nccwpck_require__.ab="//";var s={};s.decode=s.parse=__nccwpck_require__(815),s.encode=s.stringify=__nccwpck_require__(577),n.exports=s}()},8018:function(n){var o,u,s,f,g,_,y,P,b,E,S,R,w,O,j,A,C,M,I,x,L,N,D,k,U,F,H,B,W,q,z,G,V,X,K,Y,Q,$,J,Z,ee,et,er,en,ea,eo;(o={}).d=function(n,u){for(var s in u)o.o(u,s)&&!o.o(n,s)&&Object.defineProperty(n,s,{enumerable:!0,get:u[s]})},o.o=function(n,o){return Object.prototype.hasOwnProperty.call(n,o)},o.r=function(n){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(n,"__esModule",{value:!0})},void 0!==o&&(o.ab="//"),u={},o.r(u),o.d(u,{getCLS:function(){return D},getFCP:function(){return x},getFID:function(){return q},getINP:function(){return et},getLCP:function(){return en},getTTFB:function(){return eo},onCLS:function(){return D},onFCP:function(){return x},onFID:function(){return q},onINP:function(){return et},onLCP:function(){return en},onTTFB:function(){return eo}}),P=-1,b=function(n){addEventListener("pageshow",function(o){o.persisted&&(P=o.timeStamp,n(o))},!0)},E=function(){return window.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0]},S=function(){var n=E();return n&&n.activationStart||0},R=function(n,o){var u=E(),s="navigate";return P>=0?s="back-forward-cache":u&&(s=document.prerendering||S()>0?"prerender":u.type.replace(/_/g,"-")),{name:n,value:void 0===o?-1:o,rating:"good",delta:0,entries:[],id:"v3-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:s}},w=function(n,o,u){try{if(PerformanceObserver.supportedEntryTypes.includes(n)){var s=new PerformanceObserver(function(n){o(n.getEntries())});return s.observe(Object.assign({type:n,buffered:!0},u||{})),s}}catch(n){}},O=function(n,o){var T=function t(u){"pagehide"!==u.type&&"hidden"!==document.visibilityState||(n(u),o&&(removeEventListener("visibilitychange",t,!0),removeEventListener("pagehide",t,!0)))};addEventListener("visibilitychange",T,!0),addEventListener("pagehide",T,!0)},j=function(n,o,u,s){var f,g;return function(_){var y;o.value>=0&&(_||s)&&((g=o.value-(f||0))||void 0===f)&&(f=o.value,o.delta=g,o.rating=(y=o.value)>u[1]?"poor":y>u[0]?"needs-improvement":"good",n(o))}},A=-1,C=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},M=function(){O(function(n){A=n.timeStamp},!0)},I=function(){return A<0&&(A=C(),M(),b(function(){setTimeout(function(){A=C(),M()},0)})),{get firstHiddenTime(){return A}}},x=function(n,o){o=o||{};var u,s=[1800,3e3],f=I(),g=R("FCP"),c=function(n){n.forEach(function(n){"first-contentful-paint"===n.name&&(y&&y.disconnect(),n.startTime<f.firstHiddenTime&&(g.value=n.startTime-S(),g.entries.push(n),u(!0)))})},_=window.performance&&window.performance.getEntriesByName&&window.performance.getEntriesByName("first-contentful-paint")[0],y=_?null:w("paint",c);(_||y)&&(u=j(n,g,s,o.reportAllChanges),_&&c([_]),b(function(f){u=j(n,g=R("FCP"),s,o.reportAllChanges),requestAnimationFrame(function(){requestAnimationFrame(function(){g.value=performance.now()-f.timeStamp,u(!0)})})}))},L=!1,N=-1,D=function(n,o){o=o||{};var u=[.1,.25];L||(x(function(n){N=n.value}),L=!0);var s,i=function(o){N>-1&&n(o)},f=R("CLS",0),g=0,_=[],p=function(n){n.forEach(function(n){if(!n.hadRecentInput){var o=_[0],u=_[_.length-1];g&&n.startTime-u.startTime<1e3&&n.startTime-o.startTime<5e3?(g+=n.value,_.push(n)):(g=n.value,_=[n]),g>f.value&&(f.value=g,f.entries=_,s())}})},y=w("layout-shift",p);y&&(s=j(i,f,u,o.reportAllChanges),O(function(){p(y.takeRecords()),s(!0)}),b(function(){g=0,N=-1,s=j(i,f=R("CLS",0),u,o.reportAllChanges)}))},k={passive:!0,capture:!0},U=new Date,F=function(n,o){s||(s=o,f=n,g=new Date,W(removeEventListener),H())},H=function(){if(f>=0&&f<g-U){var n={entryType:"first-input",name:s.type,target:s.target,cancelable:s.cancelable,startTime:s.timeStamp,processingStart:s.timeStamp+f};_.forEach(function(o){o(n)}),_=[]}},B=function(n){if(n.cancelable){var o,u,s,f=(n.timeStamp>1e12?new Date:performance.now())-n.timeStamp;"pointerdown"==n.type?(o=function(){F(f,n),s()},u=function(){s()},s=function(){removeEventListener("pointerup",o,k),removeEventListener("pointercancel",u,k)},addEventListener("pointerup",o,k),addEventListener("pointercancel",u,k)):F(f,n)}},W=function(n){["mousedown","keydown","touchstart","pointerdown"].forEach(function(o){return n(o,B,k)})},q=function(n,o){o=o||{};var u,g=[100,300],y=I(),P=R("FID"),v=function(n){n.startTime<y.firstHiddenTime&&(P.value=n.processingStart-n.startTime,P.entries.push(n),u(!0))},m=function(n){n.forEach(v)},E=w("first-input",m);u=j(n,P,g,o.reportAllChanges),E&&O(function(){m(E.takeRecords()),E.disconnect()},!0),E&&b(function(){u=j(n,P=R("FID"),g,o.reportAllChanges),_=[],f=-1,s=null,W(addEventListener),_.push(v),H()})},z=0,G=1/0,V=0,X=function(n){n.forEach(function(n){n.interactionId&&(G=Math.min(G,n.interactionId),z=(V=Math.max(V,n.interactionId))?(V-G)/7+1:0)})},K=function(){return y?z:performance.interactionCount||0},Y=function(){"interactionCount"in performance||y||(y=w("event",X,{type:"event",buffered:!0,durationThreshold:0}))},Q=0,$=function(){return K()-Q},J=[],Z={},ee=function(n){var o=J[J.length-1],u=Z[n.interactionId];if(u||J.length<10||n.duration>o.latency){if(u)u.entries.push(n),u.latency=Math.max(u.latency,n.duration);else{var s={id:n.interactionId,latency:n.duration,entries:[n]};Z[s.id]=s,J.push(s)}J.sort(function(n,o){return o.latency-n.latency}),J.splice(10).forEach(function(n){delete Z[n.id]})}},et=function(n,o){o=o||{};var u=[200,500];Y();var s,f=R("INP"),a=function(n){n.forEach(function(n){n.interactionId&&ee(n),"first-input"!==n.entryType||J.some(function(o){return o.entries.some(function(o){return n.duration===o.duration&&n.startTime===o.startTime})})||ee(n)});var o,u=(o=Math.min(J.length-1,Math.floor($()/50)),J[o]);u&&u.latency!==f.value&&(f.value=u.latency,f.entries=u.entries,s())},g=w("event",a,{durationThreshold:o.durationThreshold||40});s=j(n,f,u,o.reportAllChanges),g&&(g.observe({type:"first-input",buffered:!0}),O(function(){a(g.takeRecords()),f.value<0&&$()>0&&(f.value=0,f.entries=[]),s(!0)}),b(function(){J=[],Q=K(),s=j(n,f=R("INP"),u,o.reportAllChanges)}))},er={},en=function(n,o){o=o||{};var u,s=[2500,4e3],f=I(),g=R("LCP"),c=function(n){var o=n[n.length-1];if(o){var s=o.startTime-S();s<f.firstHiddenTime&&(g.value=s,g.entries=[o],u())}},_=w("largest-contentful-paint",c);if(_){u=j(n,g,s,o.reportAllChanges);var v=function(){er[g.id]||(c(_.takeRecords()),_.disconnect(),er[g.id]=!0,u(!0))};["keydown","click"].forEach(function(n){addEventListener(n,v,{once:!0,capture:!0})}),O(v,!0),b(function(f){u=j(n,g=R("LCP"),s,o.reportAllChanges),requestAnimationFrame(function(){requestAnimationFrame(function(){g.value=performance.now()-f.timeStamp,er[g.id]=!0,u(!0)})})})}},ea=function e(n){document.prerendering?addEventListener("prerenderingchange",function(){return e(n)},!0):"complete"!==document.readyState?addEventListener("load",function(){return e(n)},!0):setTimeout(n,0)},eo=function(n,o){o=o||{};var u=[800,1800],s=R("TTFB"),f=j(n,s,u,o.reportAllChanges);ea(function(){var g=E();if(g){if(s.value=Math.max(g.responseStart-S(),0),s.value<0||s.value>performance.now())return;s.entries=[g],f(!0),b(function(){(f=j(n,s=R("TTFB",0),u,o.reportAllChanges))(!0)})}})},n.exports=u},9423:function(n,o){"use strict";function isAPIRoute(n){return"/api"===n||!!(null==n?void 0:n.startsWith("/api/"))}Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"isAPIRoute",{enumerable:!0,get:function(){return isAPIRoute}})},676:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),function(n,o){for(var u in o)Object.defineProperty(n,u,{enumerable:!0,get:o[u]})}(o,{default:function(){return isError},getProperError:function(){return getProperError}});let s=u(9942);function isError(n){return"object"==typeof n&&null!==n&&"name"in n&&"message"in n}function getProperError(n){return isError(n)?n:Error((0,s.isPlainObject)(n)?JSON.stringify(n):n+"")}},2407:function(n,o,u){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),function(n,o){for(var u in o)Object.defineProperty(n,u,{enumerable:!0,get:o[u]})}(o,{INTERCEPTION_ROUTE_MARKERS:function(){return f},isInterceptionRouteAppPath:function(){return isInterceptionRouteAppPath},extractInterceptionRouteInformation:function(){return extractInterceptionRouteInformation}});let s=u(9537),f=["(..)(..)","(.)","(..)","(...)"];function isInterceptionRouteAppPath(n){return void 0!==n.split("/").find(n=>f.find(o=>n.startsWith(o)))}function extractInterceptionRouteInformation(n){let o,u,g;for(let s of n.split("/"))if(u=f.find(n=>s.startsWith(n))){[o,g]=n.split(u,2);break}if(!o||!u||!g)throw Error(`Invalid interception route: ${n}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`);switch(o=(0,s.normalizeAppPath)(o),u){case"(.)":g="/"===o?`/${g}`:o+"/"+g;break;case"(..)":if("/"===o)throw Error(`Invalid interception route: ${n}. Cannot use (..) marker at the root level, use (.) instead.`);g=o.split("/").slice(0,-1).concat(g).join("/");break;case"(...)":g="/"+g;break;case"(..)(..)":let _=o.split("/");if(_.length<=2)throw Error(`Invalid interception route: ${n}. Cannot use (..)(..) marker at the root level or one level up.`);g=_.slice(0,-2).concat(g).join("/");break;default:throw Error("Invariant: unexpected marker")}return{interceptingRoute:o,interceptedRoute:g}}},2431:function(){},3219:function(n,o,u){"use strict";function _interop_require_default(n){return n&&n.__esModule?n:{default:n}}u.r(o),u.d(o,{_:function(){return _interop_require_default},_interop_require_default:function(){return _interop_require_default}})},6794:function(n,o,u){"use strict";function _getRequireWildcardCache(n){if("function"!=typeof WeakMap)return null;var o=new WeakMap,u=new WeakMap;return(_getRequireWildcardCache=function(n){return n?u:o})(n)}function _interop_require_wildcard(n,o){if(!o&&n&&n.__esModule)return n;if(null===n||"object"!=typeof n&&"function"!=typeof n)return{default:n};var u=_getRequireWildcardCache(o);if(u&&u.has(n))return u.get(n);var s={},f=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var g in n)if("default"!==g&&Object.prototype.hasOwnProperty.call(n,g)){var _=f?Object.getOwnPropertyDescriptor(n,g):null;_&&(_.get||_.set)?Object.defineProperty(s,g,_):s[g]=n[g]}return s.default=n,u&&u.set(n,s),s}u.r(o),u.d(o,{_:function(){return _interop_require_wildcard},_interop_require_wildcard:function(){return _interop_require_wildcard}})}},function(n){n.O(0,[774],function(){return n(n.s=6653)}),_N_E=n.O()}]);